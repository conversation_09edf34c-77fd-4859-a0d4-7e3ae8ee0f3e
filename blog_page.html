<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI 助教平台 - 博客编辑器</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/katex@0.13.11/dist/katex.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.5.1/styles/atom-one-dark.min.css">
    <link rel="icon" href="data:image/svg+xml,<svg xmlns=%22http://www.w3.org/2000/svg%22 viewBox=%220 0 100 100%22><text y=%22.9em%22 font-size=%2290%22>✍️</text></svg>">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&family=JetBrains+Mono:wght@400;500;600&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary-color: #667eea;
            --secondary-color: #764ba2;
            --accent-color: #f093fb;
            --success-color: #10b981;
            --warning-color: #f59e0b;
            --error-color: #ef4444;
            --text-primary: #1f2937;
            --text-secondary: #4b5563;
            --text-muted: #6b7280;
            --bg-primary: #ffffff;
            --bg-secondary: #f9fafb;
            --bg-tertiary: #f3f4f6;
            --border-color: #e5e7eb;
            --border-color-focus: #3b82f6;
            --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
            --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
            --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
            --gradient-primary: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            --gradient-accent: linear-gradient(135deg, var(--accent-color) 0%, var(--primary-color) 100%);
            --border-radius: 8px;
            --border-radius-lg: 12px;
            --border-radius-xl: 16px;
            --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        * {
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 1600px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            color: var(--text-primary);
            line-height: 1.6;
            min-height: 100vh;
        }
        /* 输入框和文本区域样式 */
        #title, #content {
            width: 100%;
            margin-bottom: 24px;
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius-lg);
            font-family: 'Inter', sans-serif;
            transition: var(--transition);
            background: var(--bg-primary);
        }

        #title {
            font-size: 20px;
            font-weight: 600;
            padding: 16px 20px;
            color: var(--text-primary);
        }

        #title:focus {
            outline: none;
            border-color: var(--border-color-focus);
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        #content {
            min-height: 500px;
            font-size: 16px;
            padding: 20px;
            resize: vertical;
            overflow-y: auto;
            line-height: 1.7;
            font-family: 'JetBrains Mono', 'Fira Code', monospace;
        }

        #content:focus {
            outline: none;
            border-color: var(--border-color-focus);
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        /* 按钮基础样式 */
        button {
            font-family: 'Inter', sans-serif;
            font-weight: 600;
            border: none;
            border-radius: var(--border-radius);
            cursor: pointer;
            transition: var(--transition);
            display: inline-flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            text-decoration: none;
            white-space: nowrap;
            position: relative;
            overflow: hidden;
        }

        button:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none !important;
        }

        button:not(:disabled):hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-lg);
        }

        button:not(:disabled):active {
            transform: translateY(0);
        }
        #button-row-1, #button-row-2 {
            display: flex;
            justify-content: flex-start; /* 改为靠左对齐 */
            margin-bottom: 10px;
        }
        #relationInput {
            background-color: #f5f5f5;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }

        #addRelationForm {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin-bottom: 25px;
        }

        .relation-input {
            flex: 1;
            min-width: 200px;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }

        .button-group {
            display: flex;
            gap: 10px;
        }

        .relation-btn {
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            transition: background-color 0.3s, transform 0.1s;
            margin-top: 10px; /* 添加上边距 */
    }

        .relation-btn:hover {
            transform: translateY(-2px);
        }

        .relation-btn:active {
            transform: translateY(0);
        }

        .submit-btn {
            background-color: #4CAF50;
            color: white;
        }

        .submit-btn:hover {
            background-color: #45a049;
        }

        .cancel-btn {
            background-color: #f44336;
            color: white;
        }

        .cancel-btn:hover {
            background-color: #da190b;
        }

        .relation-textarea {
            width: 100%;
            height: 100px;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
            margin-bottom: 15px;
            resize: vertical;
        }

        .generate-btn {
            background-color: #2196F3;
            color: white;
            width: 100%;
        }

        .generate-btn:hover {
            background-color: #0b7dda;
        }




        
        #controls {
            display: flex;
            justify-content: center;
            gap: 15px;
            margin-bottom: 20px;
        }

        .graph-btn {
            padding: 10px 20px;
            font-size: 16px;
            color: #ffffff;
            background-color: #3498db;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
        }

        .graph-btn:hover {
            background-color: #2980b9;
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
        }

        .graph-btn:active {
            transform: translateY(0);
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        #addRelationBtn {
            background-color: #2ecc71;
        }

        #addRelationBtn:hover {
            background-color: #27ae60;
        }
        #addRelationForm {
            display: none;
            margin-top: 10px;
            padding: 10px;
            border: 1px solid #ccc;
            border-radius: 5px;
        }
        #codeDisplay {
            width: 100%;
            height: 150px;
            margin-bottom: 10px;
            resize: vertical;
        }
        #garps {
            width: 100%;
            height: 600px;
        }
        #button-row-1 button, #button-row-2 button {
            flex: 0 1 auto; /* 改为自适应宽度 */
            margin-right: 10px;
            min-width: 100px; /* 设置最小宽度 */
            max-width: 150px; /* 设置最大宽度 */
        }
        button {
            font-size: 14px; /* 稍微减小字体大小 */
            padding: 8px 15px; /* 减小内边距 */
            color: white;
            border: none;
            cursor: pointer;
            white-space: nowrap; /* 防止文字换行 */
            overflow: hidden;
            text-overflow: ellipsis; /* 文字过长时显示省略号 */
        }
        #publish, #update {
            background-color: #6ef859;  /* 绿色 */
        }
        #saveDraft {
            background-color: #f85959;  /* 红色 */
        }
        #cancel {
            background-color: #5995f8;  /* 蓝色 */
        }
        #compile {
        background-color: #9b59b6;  /* 紫色 */
        }
        #compile.compiled {
        background-color: #dba5f3;  /* 浅色紫色，用于编译状态 */
        }
        #blogList {
            margin-top: 30px;
        }
        #userInfoContainer {
            background-color: #f0f8ff;
            border-radius: 10px;
            padding: 10px 15px;
            margin: 10px 0;
            display: inline-flex;
            align-items: center;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
        }
        #userInfoContainer:hover {
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
            transform: translateY(-2px);
        }
        #userIcon {
            font-size: 24px;
            margin-right: 10px;
            color: #4a90e2;
        }
        #userInfo {
            font-family: 'Arial', sans-serif;
            font-size: 16px;
            color: #333;
            font-weight: bold;
        }
        .user-name {
            color: #4a90e2;
            margin-left: 5px;
        }


        .blog-item {
            border: 1px solid #ddd;
            padding: 10px;
            margin-bottom: 10px;
        }
        .blog-item-buttons {
            display: flex;
            justify-content: flex-start;
            margin-top: 10px;
        }
        .blog-item-buttons button {
            flex: 0 1 auto;
            margin-right: 10px;
            min-width: 80px;
            max-width: 120px;
            font-size: 14px;
            padding: 5px 10px;
            color: white;
            border: none;
            cursor: pointer;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }
        .edit-btn {
            background-color: #6ef859;  /* 绿色 */
        }
        .delete-btn {
            background-color: #f85959;  /* 红色 */
        }
        #draftStatus {
            color: green;
            margin-top: 10px;
        }
        #compiledContent {
            border: 1px solid #b42424;
            padding: 10px;
            min-height: 300px;
            overflow-y: auto;
            background-color: #ffffff;
        }
        #compiledContent img {
            max-width: 100%;
            height: auto;
            display: block;
            text-align: left;
            margin-left: 0;
            margin-right: auto;
        }
        .math-block {
            display: block;
            margin: 1em 0;
            text-align: left;
        }
        .math-inline {
            font-style: italic;
        }
        button:disabled {
            opacity: 0.6;
            cursor: not-allowed;
        }
        #markdown-toolbar {
            display: flex;
            flex-wrap: wrap;
            justify-content: flex-start;
            width: 100%; /* 确保工具栏宽度为100% */
            margin-bottom: 10px;
        }

        .toolbar-btn {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 12px 8px;
            background: var(--bg-secondary);
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius-lg);
            cursor: pointer;
            font-size: 11px;
            font-weight: 500;
            min-height: 70px;
            transition: var(--transition);
            position: relative; /* 新增，确保下拉菜单绝对定位于按钮 */
            overflow: hidden;
        }

        .toolbar-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
            transition: left 0.5s;
        }

        .toolbar-btn:hover::before {
            left: 100%;
        }

        .toolbar-btn:hover {
            background: var(--bg-primary);
            border-color: var(--primary-color);
            transform: translateY(-3px);
            box-shadow: var(--shadow-md);
        }

        .toolbar-btn i {
            font-size: 20px;
            margin-bottom: 6px;
            color: var(--primary-color);
            transition: var(--transition);
        }

        .toolbar-btn:hover i {
            color: var(--secondary-color);
            transform: scale(1.1);
        }

        .toolbar-btn span {
            color: var(--text-secondary);
            text-align: center;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        #math-dropdown {
            position: relative;
        }

        .dropdown-content {
            display: none;
            position: fixed; /* 改为 fixed 定位 */
            z-index: 9999;   /* 提高 z-index */
            background: var(--bg-primary);
            min-width: 320px;
            max-width: 400px;
            max-height: 300px;
            overflow-y: auto;
            box-shadow: var(--shadow-xl);
            padding: 15px;
            border-radius: var(--border-radius-lg);
            border: 2px solid var(--primary-color);
            backdrop-filter: blur(20px);
            flex-wrap: wrap;
            justify-content: flex-start;
            gap: 8px;
        }

        .dropdown-content.show {
            display: flex !important;
            animation: fadeInScale 0.2s ease-out;
        }

        /* 自定义滚动条样式 */
        .dropdown-content::-webkit-scrollbar {
            width: 6px;
        }

        .dropdown-content::-webkit-scrollbar-track {
            background: var(--bg-tertiary);
            border-radius: 3px;
        }

        .dropdown-content::-webkit-scrollbar-thumb {
            background: var(--primary-color);
            border-radius: 3px;
        }

        .dropdown-content::-webkit-scrollbar-thumb:hover {
            background: var(--secondary-color);
        }

        @keyframes fadeInScale {
            from {
                opacity: 0;
                transform: scale(0.95);
            }
            to {
                opacity: 1;
                transform: scale(1);
            }
        }

        .dropdown-content button {
            color: var(--text-primary);
            padding: 8px 10px;
            text-decoration: none;
            display: flex;
            justify-content: center;
            align-items: center;
            width: calc(25% - 6px);
            height: 40px;
            text-align: center;
            border: 2px solid var(--border-color);
            background: var(--bg-secondary);
            cursor: pointer;
            font-size: 14px;
            font-family: 'JetBrains Mono', 'Times New Roman', serif;
            border-radius: var(--border-radius);
            transition: var(--transition);
            font-weight: 500;
            position: relative;
            overflow: hidden;
        }

        .dropdown-content button::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
            transition: left 0.3s;
        }

        .dropdown-content button:hover::before {
            left: 100%;
        }

        .dropdown-content button:hover {
            background: var(--primary-color);
            color: white;
            border-color: var(--primary-color);
            transform: translateY(-2px) scale(1.05);
            box-shadow: var(--shadow-md);
            z-index: 10;
        }

        .dropdown-content button:active {
            transform: translateY(0) scale(1);
        }

        /* 添加新的样式 */
        #color-dropdown {
            position: relative;
        }

        .color-dropdown-content {
            display: none;
            position: absolute; /* 新增 */
            left: 0;            /* 新增 */
            top: 100%;          /* 新增 */
            z-index: 1001;      /* 新增 */
            background: var(--bg-primary);
            width: 140px;
            box-shadow: var(--shadow-xl);
            padding: 12px;
            border-radius: var(--border-radius-lg);
            border: 1px solid var(--border-color);
            backdrop-filter: blur(20px);
            grid-template-columns: repeat(4, 1fr);
            gap: 8px;
        }

        .color-dropdown-content.show {
            display: grid !important;
            min-width: 160px !important;
            min-height: 120px !important;
            background: var(--bg-primary) !important;
            border: 2px solid var(--primary-color) !important;
            box-shadow: var(--shadow-xl) !important;
            z-index: 9999 !important;
            border-radius: var(--border-radius-lg) !important;
            padding: 12px !important;
            animation: fadeInScale 0.2s ease-out !important;
        }

        .color-option {
            width: 32px;
            height: 32px;
            cursor: pointer;
            border: 2px solid var(--border-color);
            border-radius: 50%;
            position: relative;
            transition: var(--transition);
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .color-option:hover {
            transform: scale(1.15);
            box-shadow: var(--shadow-lg);
            z-index: 10;
            border-color: var(--primary-color);
        }

        .color-option:active {
            transform: scale(1.05);
        }

        .color-option:hover::after {
            content: attr(title);
            position: absolute;
            bottom: 120%;
            left: 50%;
            transform: translateX(-50%);
            background: var(--text-primary);
            color: white;
            padding: 6px 10px;
            border-radius: var(--border-radius);
            font-size: 11px;
            font-weight: 500;
            white-space: nowrap;
            box-shadow: var(--shadow-md);
            z-index: 1002;
        }

        /* 添加到现有的 style 标签中 */
        .table-selector {
            position: absolute;
            background: white;
            border: 1px solid #ccc;
            padding: 5px;
            display: none;
            z-index: 1000;
        }
        .table-selector-row {
            display: flex;
        }
        .table-selector-cell {
            width: 20px;
            height: 20px;
            border: 1px solid #ddd;
            margin: 1px;
            cursor: pointer;
        }
        .table-selector-cell.selected {
            background-color: #007bff;
        }

        .toolbar-container {
            position: sticky;
            top: 0;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            z-index: 1000;
            padding: 20px 0;
            margin-bottom: 24px;
            border-radius: var(--border-radius-xl);
            box-shadow: var(--shadow-lg);
            border: 1px solid var(--border-color);
        }

        #markdown-toolbar {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(70px, 1fr));
            gap: 12px;
            padding: 0 20px;
            max-width: 100%;
        }

        pre code {
            display: block;
            background-color: #1e1e1e;
            color: #d4d4d4;
            padding: 1em;
            border-radius: 5px;
            overflow-x: auto;
        }

        /* 添加一些常见的代码高亮样式 */
        .hljs-keyword { color: #569cd6; }
        .hljs-string { color: #ce9178; }
        .hljs-comment { color: #6a9955; }
        .hljs-function { color: #dcdcaa; }
        .hljs-number { color: #b5cea8; }
        .hljs-operator { color: #d4d4d4; }
        .hljs-class { color: #4ec9b0; }
        .editor-header {
            text-align: center;
            margin: 30px 0 40px;
            position: relative;
            padding: 40px 20px;
            background: var(--gradient-primary);
            border-radius: var(--border-radius-xl);
            color: white;
            box-shadow: var(--shadow-lg);
        }

        .editor-title {
            font-family: 'Inter', sans-serif;
            font-size: 3rem;
            font-weight: 800;
            margin: 0;
            text-transform: uppercase;
            letter-spacing: 2px;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }

        .editor-subtitle {
            font-family: 'Inter', sans-serif;
            font-size: 1.2rem;
            font-weight: 400;
            margin-top: 10px;
            opacity: 0.9;
        }

        .editor-header::after {
            content: '';
            position: absolute;
            bottom: -8px;
            left: 50%;
            transform: translateX(-50%);
            width: 120px;
            height: 4px;
            background: var(--gradient-accent);
            border-radius: 2px;
            box-shadow: 0 2px 8px rgba(240, 147, 251, 0.4);
        }

        /* 用户信息容器样式 */
        #userInfoContainer {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 15px 20px;
            background: var(--bg-primary);
            border-radius: var(--border-radius-lg);
            box-shadow: var(--shadow-md);
            border: 1px solid var(--border-color);
            margin-bottom: 20px;
        }

        #userIcon {
            font-size: 24px;
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: var(--gradient-primary);
            border-radius: 50%;
            color: white;
        }

        #userInfo {
            font-weight: 600;
            color: var(--text-primary);
        }

        .user-name {
            color: var(--primary-color);
            font-weight: 700;
        }

        /* 上传次数容器样式 */
        #remainingUploadsContainer {
            margin: 20px 0;
            padding: 25px;
            background: var(--bg-primary);
            border-radius: var(--border-radius-xl);
            box-shadow: var(--shadow-md);
            border: 1px solid var(--border-color);
        }

        #remainingUploadsContainer h3 {
            margin: 0 0 20px 0;
            color: var(--text-primary);
            font-size: 1.3em;
            font-weight: 700;
            display: flex;
            align-items: center;
        }

        #remainingUploadsContainer h3::before {
            content: '📊';
            margin-right: 10px;
            font-size: 1.2em;
        }

        /* 博客列表样式优化 */
        #blogList {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 30px;
        }
        .blog-list-container {
            margin: 20px 0;
            padding: 0 20px;
        }
        .blog-item {
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            padding: 20px;
            background-color: #ffffff;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
        }

        .blog-item:hover {
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
            transform: translateY(-3px);
        }

        .blog-item h3 {
            font-size: 20px;
            margin-top: 0;
            margin-bottom: 10px;
            color: #2c3e50;
        }

        .blog-item p {
            font-size: 14px;
            color: #7f8c8d;
            margin: 5px 0;
        }

        .blog-item-buttons {
            display: flex;
            justify-content: space-between;
            margin-top: 15px;
        }

        .blog-item-buttons button {
            flex: 1;
            margin: 0 5px;
            padding: 8px 15px;
            font-size: 14px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            transition: background-color 0.3s ease;
        }

        .edit-btn {
            background-color: #3498db;
            color: white;
        }

        .edit-btn:hover {
            background-color: #2980b9;
        }

        .delete-btn {
            background-color: #e74c3c;
            color: white;
        }

        .delete-btn:hover {
            background-color: #c0392b;
        }
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            overflow: auto;
            background-color: rgba(0,0,0,0.4);
        }

        .modal-content {
            background-color: #fefefe;
            margin: 10% auto;
            padding: 20px;
            border: 1px solid #888;
            width: 80%;
            max-width: 500px;
            border-radius: 8px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }

        .close {
            color: #aaa;
            float: right;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
        }

        .close:hover,
        .close:focus {
            color: #000;
            text-decoration: none;
        }

        h2, h3 {
            color: #333;
            margin-bottom: 15px;
        }

        .btn {
            padding: 10px 15px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            transition: background-color 0.3s;
        }

        .btn-primary {
            background: var(--gradient-primary);
            color: white;
            padding: 12px 24px;
            font-size: 15px;
            border-radius: 25px;
            box-shadow: var(--shadow-md);
        }

        .btn-primary:hover {
            box-shadow: var(--shadow-lg);
        }

        .btn-success {
            background: linear-gradient(135deg, var(--success-color), #059669);
            color: white;
            padding: 12px 24px;
            font-size: 15px;
            border-radius: 25px;
            box-shadow: var(--shadow-md);
        }

        .btn-success:hover {
            box-shadow: var(--shadow-lg);
        }

        .btn-info {
            background: linear-gradient(135deg, #06b6d4, #0891b2);
            color: white;
            padding: 12px 24px;
            font-size: 15px;
            border-radius: 25px;
            box-shadow: var(--shadow-md);
        }

        .btn-info:hover {
            box-shadow: var(--shadow-lg);
        }

        .btn:hover {
            opacity: 0.8;
        }

        .btn:disabled {
            background-color: #cccccc;
            cursor: not-allowed;
        }

        .file-name {
            margin: 10px 0;
            font-style: italic;
        }

        .progress-bar {
            width: 100%;
            background-color: #e0e0e0;
            padding: 3px;
            border-radius: 3px;
            box-shadow: inset 0 1px 3px rgba(0, 0, 0, .2);
        }

        .progress-bar .progress {
            width: 0;
            height: 20px;
            background-color: #4CAF50;
            border-radius: 3px;
            transition: width 0.3s;
        }

        #uploadPercentage {
            position: absolute;
            right: 5px;
            color: white;
        }

        .divider {
            text-align: center;
            margin: 20px 0;
            overflow: hidden;
        }

        .divider span {
            display: inline-block;
            position: relative;
        }

        .divider span:before,
        .divider span:after {
            content: "";
            position: absolute;
            top: 50%;
            width: 100vw;
            height: 1px;
            background: #ddd;
        }

        .divider span:before {
            right: 100%;
            margin-right: 15px;
        }

        .divider span:after {
            left: 100%;
            margin-left: 15px;
        }

        .input-group-vertical {
            display: flex;
            flex-direction: column;
            gap: 10px;  /* 在输入框和按钮之间添加间距 */
        }

        .input-group-vertical input {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }

        .input-group-vertical button {
            width: 100%;
            padding: 10px;
            border: none;
            border-radius: 4px;
            background-color: #17a2b8;
            color: white;
            font-size: 14px;
            cursor: pointer;
            transition: background-color 0.3s;
        }

        .input-group-vertical button:hover {
            background-color: #138496;
        }

        .input-group-vertical button i {
            margin-right: 5px;
        }

        /* 预览面板样式 */
        .preview-placeholder {
            text-align: center;
            color: var(--text-muted);
            font-style: italic;
            padding: 60px 20px;
            background: var(--bg-tertiary);
            border-radius: var(--border-radius);
            border: 2px dashed var(--border-color);
        }

        #compiledContent {
            line-height: 1.8;
            color: var(--text-primary);
        }

        #compiledContent h1, #compiledContent h2, #compiledContent h3 {
            color: var(--primary-color);
            margin-top: 1.5em;
            margin-bottom: 0.5em;
        }

        #compiledContent h1 {
            font-size: 2.2em;
            border-bottom: 2px solid var(--primary-color);
            padding-bottom: 10px;
        }

        #compiledContent h2 {
            font-size: 1.8em;
        }

        #compiledContent h3 {
            font-size: 1.4em;
        }

        #compiledContent blockquote {
            border-left: 4px solid var(--primary-color) !important;
            margin: 1.5em 0 !important;
            padding: 1em 1.5em !important;
            background: var(--bg-tertiary) !important;
            border-radius: 0 var(--border-radius) var(--border-radius) 0 !important;
            font-style: italic !important;
            color: var(--text-secondary) !important;
        }

        .video-container {
            position: relative;
            width: 100%;
            max-width: 100%;
            margin: 20px 0;
            border-radius: var(--border-radius);
            overflow: hidden;
            box-shadow: var(--shadow-md);
        }

        .video-container::before {
            content: "";
            display: block;
            padding-top: 56.25%; /* 16:9 宽高比 */
        }

        .video-container iframe,
        .video-container video {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
        }

        /* 响应式设计 */
        @media (max-width: 1024px) {
            .editor-content.split-view {
                flex-direction: column;
            }

            .editor-content.split-view .preview-panel {
                border-left: none;
                border-top: 1px solid var(--border-color);
                height: 400px; /* 移动端较小的高度 */
                max-height: 400px;
            }

            .editor-content.split-view .editor-panel {
                height: 400px;
                max-height: 400px;
            }

            /* 移动端分屏模式下的编译内容 */
            .editor-content.split-view #compiledContent {
                max-height: 350px; /* 移动端较小的高度 */
            }

            #markdown-toolbar {
                grid-template-columns: repeat(auto-fit, minmax(60px, 1fr));
                gap: 8px;
            }

            .toolbar-btn {
                min-height: 60px;
                font-size: 10px;
            }

            .toolbar-btn i {
                font-size: 18px;
            }
        }

        @media (max-width: 768px) {
            body {
                padding: 15px;
            }

            .editor-header {
                padding: 30px 15px;
            }

            .editor-title {
                font-size: 2.2rem;
            }

            .editor-controls {
                flex-direction: column;
                gap: 15px;
                align-items: stretch;
            }

            .editor-tabs {
                justify-content: center;
            }

            .editor-status {
                justify-content: center;
                font-size: 13px;
            }

            .remaining-uploads-row {
                grid-template-columns: 1fr;
                gap: 15px;
            }

            .editor-content {
                min-height: 400px;
            }

            .editor-panel, .preview-panel {
                padding: 20px;
            }
        }

        @media (max-width: 480px) {
            .editor-title {
                font-size: 1.8rem;
            }

            .tab-btn {
                padding: 8px 16px;
                font-size: 13px;
            }

            #markdown-toolbar {
                grid-template-columns: repeat(auto-fit, minmax(50px, 1fr));
                gap: 6px;
                padding: 0 15px;
            }

            .toolbar-btn {
                min-height: 50px;
                font-size: 9px;
                padding: 8px 4px;
            }

            .toolbar-btn i {
                font-size: 16px;
                margin-bottom: 4px;
            }
        }
        .remaining-uploads-row {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 20px;
        }

        .remaining-upload-item {
            text-align: center;
            padding: 20px;
            background: var(--bg-secondary);
            border-radius: var(--border-radius-lg);
            border: 2px solid var(--border-color);
            transition: var(--transition);
        }

        .remaining-upload-item:hover {
            border-color: var(--primary-color);
            transform: translateY(-2px);
            box-shadow: var(--shadow-md);
        }

        .upload-label {
            font-weight: 600;
            color: var(--text-secondary);
            display: block;
            margin-bottom: 8px;
            font-size: 0.95em;
        }

        .upload-count {
            font-size: 2em;
            font-weight: 800;
            color: var(--primary-color);
            display: block;
        }
        /* 编辑器主体样式 */
        .editor-main {
            background: var(--bg-primary);
            border-radius: var(--border-radius-xl);
            box-shadow: var(--shadow-lg);
            border: 1px solid var(--border-color);
            overflow: hidden;
            margin-bottom: 30px;
        }

        .editor-controls {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 20px 25px;
            background: var(--bg-secondary);
            border-bottom: 1px solid var(--border-color);
        }

        .editor-tabs {
            display: flex;
            gap: 8px;
        }

        .tab-btn {
            padding: 10px 20px;
            background: transparent;
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius);
            color: var(--text-secondary);
            font-weight: 600;
            font-size: 14px;
            cursor: pointer;
            transition: var(--transition);
        }

        .tab-btn.active {
            background: var(--gradient-primary);
            border-color: var(--primary-color);
            color: white;
            box-shadow: var(--shadow-md);
        }

        .tab-btn:not(.active):hover {
            border-color: var(--primary-color);
            color: var(--primary-color);
        }

        .editor-status {
            display: flex;
            align-items: center;
            gap: 20px;
            font-size: 14px;
        }

        .auto-save-status {
            color: var(--success-color);
            font-weight: 500;
        }

        .auto-save-status.saving {
            color: var(--warning-color);
        }

        .word-count {
            color: var(--text-muted);
            font-weight: 500;
        }

        .editor-content {
            display: flex;
            min-height: 600px;
        }

        .editor-panel {
            flex: 1;
            padding: 25px;
            display: flex;
            flex-direction: column;
            gap: 20px;
        }

        .preview-panel {
            flex: 1;
            padding: 25px;
            background: var(--bg-secondary);
            border-left: 1px solid var(--border-color);
            overflow-y: auto;
        }

        .editor-content.split-view .editor-panel,
        .editor-content.split-view .preview-panel {
            flex: 1;
            height: 600px; /* 设置固定高度以显示滚动条 */
        }

        .editor-content.split-view .preview-panel {
            border-left: 1px solid var(--border-color);
            overflow-y: auto; /* 确保有垂直滚动条 */
            max-height: 600px; /* 确保最大高度限制 */
        }

        .editor-content.split-view .editor-panel {
            overflow-y: auto; /* 编辑面板也需要滚动条 */
            max-height: 600px;
        }

        /* 分屏模式下的滚动条样式 */
        .editor-content.split-view .preview-panel::-webkit-scrollbar,
        .editor-content.split-view .editor-panel::-webkit-scrollbar,
        .editor-content.split-view #content::-webkit-scrollbar {
            width: 8px;
        }

        .editor-content.split-view .preview-panel::-webkit-scrollbar-track,
        .editor-content.split-view .editor-panel::-webkit-scrollbar-track,
        .editor-content.split-view #content::-webkit-scrollbar-track {
            background: var(--bg-secondary);
            border-radius: 4px;
        }

        .editor-content.split-view .preview-panel::-webkit-scrollbar-thumb,
        .editor-content.split-view .editor-panel::-webkit-scrollbar-thumb,
        .editor-content.split-view #content::-webkit-scrollbar-thumb {
            background: var(--primary-color);
            border-radius: 4px;
            opacity: 0.7;
        }

        .editor-content.split-view .preview-panel::-webkit-scrollbar-thumb:hover,
        .editor-content.split-view .editor-panel::-webkit-scrollbar-thumb:hover,
        .editor-content.split-view #content::-webkit-scrollbar-thumb:hover {
            background: var(--secondary-color);
            opacity: 1;
        }

        /* 分屏模式下的编译内容样式 */
        .editor-content.split-view #compiledContent {
            height: 100%;
            max-height: 550px; /* 留出一些空间给预览面板的padding */
            overflow-y: auto;
            padding: 20px;
            background: var(--bg-primary);
            border: 1px solid var(--border-color);
            border-radius: var(--border-radius);
        }

        .editor-content.split-view #compiledContent::-webkit-scrollbar {
            width: 8px;
        }

        .editor-content.split-view #compiledContent::-webkit-scrollbar-track {
            background: var(--bg-secondary);
            border-radius: 4px;
        }

        .editor-content.split-view #compiledContent::-webkit-scrollbar-thumb {
            background: var(--primary-color);
            border-radius: 4px;
            opacity: 0.7;
        }

        .editor-content.split-view #compiledContent::-webkit-scrollbar-thumb:hover {
            background: var(--secondary-color);
            opacity: 1;
        }

        #colorPicker {
            position: fixed;
            display: none;
            grid-template-columns: repeat(6, 1fr);
            gap: 8px;
            padding: 15px;
            background: var(--bg-primary);
            border: 1px solid var(--border-color);
            border-radius: var(--border-radius-lg);
            box-shadow: var(--shadow-xl);
            z-index: 1000;
            backdrop-filter: blur(20px);
        }

        .color-option {
            width: 28px;
            height: 28px;
            border-radius: var(--border-radius);
            cursor: pointer;
            border: 2px solid var(--border-color);
            transition: var(--transition);
        }

        .color-option:hover {
            transform: scale(1.1);
            box-shadow: var(--shadow-md);
        }

        .text-box {
            display: inline-block;
            border-left: 6px solid;
            border-top: 1px solid;
            border-right: 1px solid;
            border-bottom: 1px solid;
            padding: 10px 15px;
            margin: 10px 0;
        }

        .text-box p {
            margin: 0;
        }

        .modal {
            display: none;
            position: fixed;
            z-index: 2000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            overflow: auto;
            background-color: rgba(0,0,0,0.5);
            backdrop-filter: blur(5px);
        }

        .modal-content {
            background-color: #fefefe;
            margin: 15% auto;
            padding: 20px;
            border: 1px solid #888;
            width: 80%;
            max-width: 500px;
        }

        .close {
            color: #aaa;
            float: right;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
        }

        .close:hover,
        .close:focus {
            color: black;
            text-decoration: none;
            cursor: pointer;
        }

        .input-group {
            margin-bottom: 15px;
        }

        .input-group label {
            display: block;
            margin-bottom: 5px;
        }

        .input-group input,
        .input-group select,
        .input-group textarea {
            width: 100%;
            padding: 5px;
        }
        .blog-category-select {
            padding: 8px 15px;
            font-size: 14px;
            border: 1px solid #ddd;
            border-radius: 4px;
            background-color: white;
            color: #333;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-right: 10px;
            min-width: 150px;
            outline: none;
            appearance: none;
            -webkit-appearance: none;
            -moz-appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 8px center;
            background-size: 16px;
            padding-right: 35px;
        }

        .blog-category-select:hover {
            border-color: #4CAF50;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }

        .blog-category-select:focus {
            border-color: #4CAF50;
            box-shadow: 0 0 0 2px rgba(76,175,80,0.2);
        }

        .blog-category-select option {
            padding: 10px;
            background-color: white;
            color: #333;
        }

        .blog-category-select option:hover {
            background-color: #f5f5f5;
        }

        /* 禁用选项的样式 */
        .blog-category-select option[value=""] {
            color: #999;
        }

        /* 适配深色主题 */
        @media (prefers-color-scheme: dark) {
            .blog-category-select {
                background-color: #2c2c2c;
                color: #fff;
                border-color: #444;
            }

            .blog-category-select option {
                background-color: #2c2c2c;
                color: #fff;
            }

            .blog-category-select:hover {
                border-color: #6ef859;
            }

            .blog-category-select:focus {
                border-color: #6ef859;
                box-shadow: 0 0 0 2px rgba(110,248,89,0.2);
            }
        } 
        .blog-meta {
            margin: 10px 0;
        }

        .category-tag {
            display: inline-block;
            padding: 3px 8px;
            background-color: #e9ecef;
            color: #495057;
            border-radius: 4px;
            font-size: 12px;
            margin-bottom: 8px;
        }

        .error-message {
            color: #dc3545;
            text-align: center;
            padding: 20px;
            background-color: #f8d7da;
            border-radius: 4px;
            margin: 10px 0;
        }       
        .empty-blog-list {
            text-align: center;
            padding: 40px 20px;
            background-color: #f8f9fa;
            border-radius: 8px;
            margin: 20px 0;
        }

        .empty-blog-list i.fa-pencil-alt {
            font-size: 48px;
            color: #6c757d;
            margin-bottom: 16px;
        }

        .empty-blog-list p {
            color: #6c757d;
            font-size: 16px;
            margin-bottom: 20px;
        }

        .start-writing-btn {
            background-color: #28a745;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            transition: background-color 0.2s;
        }

        .start-writing-btn:hover {
            background-color: #218838;
        }

        .start-writing-btn i {
            margin-right: 8px;
        }  
    </style>
        <script src="https://cdn.jsdelivr.net/npm/marked/marked.min.js"></script>
        <script>
            if (typeof marked === 'undefined') {
                console.log("CDN加载失败：marked，尝试加载本地版本");
                document.write('<script src="/api/static/lib/marked.min.js"><\/script>');
            }
        </script>
        
        <script src="https://cdn.jsdelivr.net/npm/katex@0.13.11/dist/katex.min.js"></script>
        <script>
            if (typeof katex === 'undefined') {
                console.log("CDN加载失败：katex，尝试加载本地版本");
                document.write('<script src="/api/static/lib/katex.min.js"><\/script>');
            }
        </script>
        
        <script src="https://cdn.jsdelivr.net/npm/katex@0.13.11/dist/contrib/auto-render.min.js"></script>
        <script>
            if (typeof renderMathInElement === 'undefined') {
                console.log("CDN加载失败：renderMathInElement，尝试加载本地版本");
                document.write('<script src="/api/static/lib/auto-render.min.js"><\/script>');
            }
        </script>
        
        <!-- CDN版本的 mermaid 和备用加载逻辑 -->
        <script src="https://cdn.jsdelivr.net/npm/mermaid@11.3.0/dist/mermaid.min.js"></script>
        <script>
            if (typeof mermaid === 'undefined') {
                console.log("CDN加载失败：mermaid，尝试加载本地版本");
                document.write('<script src="/api/static/lib/mermaid.min.js"><\/script>');
            }
        </script>
        
        <!-- 确保 mermaid 加载完成后再初始化 -->
        <script>
            function initializeMermaid() {
                if (typeof mermaid === 'undefined') {
                    console.log("等待 mermaid 加载...");
                    setTimeout(initializeMermaid, 100);
                    return;
                }
                console.log("mermaid 已加载，开始初始化");
                try {
                    mermaid.initialize({
                        startOnLoad: true,
                        theme: 'neutral',
                        securityLevel: 'loose',
                        flowchart: {
                            useMaxWidth: false,
                            htmlLabels: true
                        },
                        logLevel: 'debug'  // 添加这行以获取更多调试信息
                    });
                    console.log("mermaid 初始化成功");
                } catch (error) {
                    console.error("mermaid 初始化失败:", error);
                }
            }
            // 页面加载完成后初始化 mermaid
            window.addEventListener('load', initializeMermaid);
        </script>
        <script src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.5.1/highlight.min.js"></script>
        <script>
            if (typeof hljs === 'undefined') {
                console.log("CDN加载失败：highlight.js，尝试加载本地版本");
                document.write('<script src="/api/static/lib/highlight.min.js"><\/script>');
            }
        </script>
        
        <script src="https://assets.pyecharts.org/assets/echarts.min.js"></script>
        <script>
            if (typeof echarts === 'undefined') {
                console.log("CDN加载失败：echarts，尝试加载本地版本");
                document.write('<script src="/api/static/lib/echarts.min.js"><\/script>');
            }
        </script>
        
        <!-- 移除了可能导致冲突的 pyecharts main.js -->
        
        <!-- 内联版本的 marked.js (最后的备用选项) -->
        <script>
            setTimeout(function() {
                if (typeof marked === 'undefined') {
                    console.log("本地marked.js加载失败，使用内联版本");
                    // 内联marked.js的简化版本
                    (function() {
                        // 最小化的marked核心实现
                        window.marked = (function() {
                            var _escape = function(html) {
                                return html
                                    .replace(/&/g, '&amp;')
                                    .replace(/</g, '&lt;')
                                    .replace(/>/g, '&gt;')
                                    .replace(/"/g, '&quot;')
                                    .replace(/'/g, '&#39;');
                            };
                            
                            var _parseInline = function(text) {
                                // 处理行内格式 (粗体, 斜体, 链接, 行内代码等)
                                return text
                                    // 粗体
                                    .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
                                    // 斜体
                                    .replace(/\*(.*?)\*/g, '<em>$1</em>')
                                    // 行内代码
                                    .replace(/`(.*?)`/g, '<code>$1</code>')
                                    // 链接 [text](url)
                                    .replace(/\[([^\]]+)\]\(([^)]+)\)/g, '<a href="$2">$1</a>');
                            };
                            
                            var _parseBlock = function(text) {
                                var lines = text.split('\n');
                                var html = '';
                                var i = 0;
                                
                                while (i < lines.length) {
                                    var line = lines[i];
                                    
                                    // 标题 (# 样式)
                                    if (/^#{1,6}\s+(.+)$/.test(line)) {
                                        var match = line.match(/^(#{1,6})\s+(.+)$/);
                                        var level = match[1].length;
                                        var content = _parseInline(match[2]);
                                        html += '<h' + level + '>' + content + '</h' + level + '>\n';
                                        i++;
                                        continue;
                                    }
                                    
                                    // 段落
                                    if (line.trim().length > 0) {
                                        var paragraph = line;
                                        i++;
                                        
                                        // 合并多行段落
                                        while (i < lines.length && lines[i].trim().length > 0) {
                                            paragraph += '\n' + lines[i];
                                            i++;
                                        }
                                        
                                        html += '<p>' + _parseInline(paragraph) + '</p>\n';
                                        continue;
                                    }
                                    
                                    // 空行
                                    i++;
                                }
                                
                                return html;
                            };
                            
                            return {
                                parse: function(markdown) {
                                    return _parseBlock(markdown);
                                },
                                setOptions: function() {
                                    // 模拟setOptions方法，实际上不做任何事
                                    return this;
                                }
                            };
                        })();
                        console.log("已加载内联marked.js");
                    })();
                } else {
                    console.log("marked.js已成功加载");
                }
            }, 500);
        </script>
        
        <script>
        // 删除旧的 mermaid 初始化代码
        // mermaid.initialize({
        //     startOnLoad: true,
        //     theme: 'neutral',
        //     securityLevel: 'loose',
        //     flowchart: {
        //         useMaxWidth: false,
        //         htmlLabels: true
        //     },
        //     logLevel: 'debug'  // 添加这行以获取更多调试信息
        // });

        // 配置 marked.js 使用 highlight.js
        marked.setOptions({
            highlight: function(code, lang) {
                if (lang && hljs.getLanguage(lang)) {
                    return hljs.highlight(lang, code).value;
                } else {
                    return hljs.highlightAuto(code).value;
                }
            }
        });
    </script>
</head>
<body>
    <header class="editor-header">
        <h1 class="editor-title">博客编辑器</h1>
        <p class="editor-subtitle">创作你的精彩内容</p>
    </header>
    <div id="userInfoContainer">
        <span id="userIcon">👤</span>
        <span id="userInfo">当前用户: <span class="user-name"></span></span>
    </div>
    <div id="remainingUploadsContainer" style="margin-top: 10px; padding: 10px; background-color: #f0f8ff; border-radius: 5px;">
        <h3 style="margin-top: 0; margin-bottom: 10px;">今日剩余上传次数：</h3>
        <div class="remaining-uploads-row">
            <div class="remaining-upload-item">
                <span class="upload-label">图片：</span>
                <span id="remainingImageUploads" class="upload-count"></span>
            </div>
            <div class="remaining-upload-item">
                <span class="upload-label">视频：</span>
                <span id="remainingVideoUploads" class="upload-count"></span>
            </div>
            <div class="remaining-upload-item">
                <span class="upload-label">附件：</span>
                <span id="remainingAttachmentUploads" class="upload-count"></span>
            </div>
        </div>
    </div>
    <div class="toolbar-container">
        <div id="markdown-toolbar">
            <div class="toolbar-btn" data-action="toc" title="目录"><i class="fas fa-list-alt"></i><span>目录</span></div>

            <div class="toolbar-btn" id="color-dropdown" title="字体颜色">
                <i class="fas fa-palette"></i><span>颜色</span>
                <!-- 删除原有的 .color-dropdown-content -->
            </div>

            <div class="toolbar-btn" data-action="bold" title="加粗"><i class="fas fa-bold"></i><span>加粗</span></div>
            <div class="toolbar-btn" data-action="italic" title="斜体"><i class="fas fa-italic"></i><span>斜体</span></div>
            <div class="toolbar-btn" data-action="heading" title="标题"><i class="fas fa-heading"></i><span>标题</span></div>
            <div class="toolbar-btn" id="textBoxBtn" title="插入文本框"><i class="fas fa-square"></i><span>文本框</span></div>
            <div class="toolbar-btn" data-action="strikethrough" title="删除线"><i class="fas fa-strikethrough"></i><span>删除线</span></div>
            <div class="toolbar-btn" data-action="unordered-list" title="无序列表"><i class="fas fa-list-ul"></i><span>无序</span></div>
            <div class="toolbar-btn" data-action="ordered-list" title="有序列表"><i class="fas fa-list-ol"></i><span>有序</span></div>
            <div class="toolbar-btn" data-action="task-list" title="待办列表"><i class="fas fa-tasks"></i><span>待办</span></div>
            <div class="toolbar-btn" data-action="blockquote" title="引用"><i class="fas fa-quote-right"></i><span>引用</span></div>
            <div class="toolbar-btn" data-action="code-block" title="代码块"><i class="fas fa-code"></i><span>代码块</span></div>
            <div class="toolbar-btn" data-action="image" title="图片"><i class="fas fa-image"></i><span>图片</span></div>
            <input type="file" id="imageUpload" style="display: none;" accept="image/*">
            <div class="toolbar-btn" data-action="upload-video" title="上传视频">
                <i class="fas fa-video"></i><span>视频</span>
            </div>
            


            <div class="toolbar-btn" data-action="table" title="表格"><i class="fas fa-table"></i><span>表格</span></div>
            <div class="toolbar-btn" data-action="link" title="超链接"><i class="fas fa-link"></i><span>超链接</span></div>

            <div class="toolbar-btn" data-action="attachment" title="上传附件">
            <i class="fas fa-paperclip"></i><span>附件</span>
            </div>
            <input type="file" id="attachmentInput" style="display: none;" accept=".zip,.rar,.7z">
             
                <div class="toolbar-btn" id="math-dropdown" title="插入数学公式">
                    <i class="fas fa-square-root-alt"></i><span>公式</span>
               </div>

               <div class="toolbar-btn" data-action="flowchart" title="插入流程图"><i class="fas fa-project-diagram"></i><span>流程图</span></div>
        </div>
    </div>

                <!-- 编辑器主体区域 -->
                <div class="editor-main">
                    <div class="editor-controls">
                        <div class="editor-tabs">
                            <button id="editTab" class="tab-btn active" onclick="switchTab('edit')">
                                <i class="fas fa-edit"></i> 编辑
                            </button>
                            <button id="previewTab" class="tab-btn" onclick="switchTab('preview')">
                                <i class="fas fa-eye"></i> 预览
                            </button>
                            <button id="splitTab" class="tab-btn" onclick="switchTab('split')">
                                <i class="fas fa-columns"></i> 分屏
                            </button>
                        </div>
                        <div class="editor-status">
                            <span id="autoSaveStatus" class="auto-save-status">
                                <i class="fas fa-save"></i> 自动保存已开启
                            </span>
                            <span id="wordCount" class="word-count">字数: 0</span>
                        </div>
                    </div>

                    <div class="editor-content">
                        <div class="editor-panel" id="editorPanel">
                            <input type="text" id="title" placeholder="请输入文章标题 (5 ~ 100个字)" minlength="5" maxlength="100" required>
                            <textarea id="content" placeholder="请输入文章内容"></textarea>
                        </div>
                        <div class="preview-panel" id="previewPanel" style="display: none;">
                            <div id="compiledContent"></div>
                        </div>
                    </div>
                </div>
                <div id="button-row-1">
                    <select id="blogCategory" class="blog-category-select" required>
                        <option value="" disabled selected>请选择分类</option>
                    </select>
                    <button id="publish" disabled>发布博客</button>
                    <button id="saveDraft">保存草稿</button>
                </div>
                <div id="button-row-2">
                    <button id="update" style="display: none;">更新博客</button>
                    <button id="cancel" style="display: none;">取消编辑</button>
                </div>
                <p id="draftStatus"></p>


                <body>
                    <div id="controls">
                        <button id="addRelationBtn" class="graph-btn">添加关系</button>
                    </div>
                    
                    <div id="relationInput">
                        <div id="addRelationForm">
                            <input type="text" id="sourceInput" class="relation-input" placeholder="源节点">
                            <input type="text" id="targetInput" class="relation-input" placeholder="目标节点">
                            <input type="text" id="valueInput" class="relation-input" placeholder="关系">
                            <div class="button-group">
                                <button id="submitRelation" class="relation-btn submit-btn">确认</button>
                                <button id="cancelRelation" class="relation-btn cancel-btn">取消</button>
                            </div>
                        </div>
                        <textarea id="codeDisplay" class="relation-textarea" placeholder="在这里输入或编辑关系，格式：源节点,目标节点,关系"></textarea>
                        <button id="generateGraphBtn" class="relation-btn generate-btn">生成图谱</button>
                    </div>
                    
                    <div id="garps"></div>

           <h1 class="editor-title">我的博客列表</h1>
          <div id="blogList"></div>

            <!-- 颜色选择器 -->
            <div id="colorPicker" class="color-picker" style="display: none;">
                <!-- 颜色选项将通过 JavaScript 动态添加 -->
            </div>

            <!-- 视频上传模态框 -->
            <div id="videoUploadModal" class="modal">
                <div class="modal-content">
                    <span class="close">&times;</span>
                    <h2>添加视频</h2>
                    <input type="number" id="videoWidth" value="640" min="100" max="1920" step="10">
                    <label for="videoWidth">视频宽度 (像素)</label>
                    <div class="upload-section">
                        <h3>上传本地视频</h3>
                        <input type="file" id="videoUpload" accept="video/mp4" style="display: none;">
                        <button id="selectVideoBtn" class="btn btn-primary">
                            <i class="fas fa-file-upload"></i> 选择视频文件
                        </button>
                        <p id="selectedFileName" class="file-name"></p>
                        <button id="uploadVideoBtn" class="btn btn-success" disabled>
                            <i class="fas fa-cloud-upload-alt"></i> 上传视频
                        </button>
                        <div id="uploadProgress" class="progress-bar" style="display: none;">
                            <div class="progress"></div>
                            <span id="uploadPercentage">0%</span>
                        </div>
                    </div>
                    <div class="divider">
                        <span>或者</span>
                    </div>
                    <div class="online-video-section">
                        <h3>插入在线视频</h3>
                        <div class="input-group-vertical">
                            <input type="text" id="onlineVideoUrl" placeholder="输入在线视频URL">
                            <button id="insertOnlineVideoBtn" class="btn btn-info">
                                <i class="fas fa-link"></i> 插入
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 文本框模态框 -->
            <div id="textBoxModal" class="modal text-box-modal" style="display: none;">
                <div class="modal-content">
                    <span class="close">&times;</span>
                    <h2>插入文本框</h2>
                    <div class="input-group">
                        <label for="textBoxBackgroundColor">背景颜色:</label>
                        <input type="text" id="textBoxBackgroundColor" readonly>
                    </div>
                    <div class="input-group">
                        <label for="textBoxContent">内容:</label>
                        <textarea id="textBoxContent" rows="4" placeholder="在这里输入文本,支持Markdown语法和数学公式"></textarea>
                    </div>
                    <button id="insertTextBox" class="btn btn-primary">插入</button>
                </div>
            </div>









     <script>
        // 获取user和key参数
        const urlParams = new URLSearchParams(window.location.search);
        const user = urlParams.get('user');
        const key = urlParams.get('key');
        const token = urlParams.get('token');
        // 在文件顶部添加调试信息
        // 添加调试信息
        console.log('Script initialized with:', {
            user: user,
            key: key,
            token: token,
            url: window.location.href
        });



        // 显示用户信息
        if (user) {
            document.querySelector('#userInfo .user-name').textContent = user;
            document.getElementById('userInfoContainer').style.display = 'inline-flex';
        } else {
            document.getElementById('userInfoContainer').style.display = 'none';
        }


        let currentEditingFilename = null;


// 加载用户博客列表

function loadUserBlogs() {
            console.log('loadUserBlogs called');
            console.log('Current user:', user);
            
            const apiUrl = `/api/get_user_blogs/${user}`;
            console.log('Requesting URL:', apiUrl);
            
            fetch(apiUrl, {
                    method: 'GET',
                    headers: {
                        'Cache-Control': 'no-cache',
                        'Pragma': 'no-cache'
                    },
                    cache: 'no-store'
                })
                .then(response => {
                    console.log('Response received:', response.status);
                    return response.json();
                })
                .then(data => {
                    console.log('Data received:', data);
                    if (data.success) {
                        const blogList = document.getElementById('blogList');
                        if (!blogList) {
                            console.error('Blog list element not found!');
                            return;
                        }
                        
                        blogList.innerHTML = '';
                        data.blogs.forEach(blog => {
                            const blogItem = document.createElement('div');
                            blogItem.className = 'blog-item';
                            blogItem.innerHTML = `
                                <h3>${blog.title}</h3>
                                <div class="blog-meta">
                                    <span class="category-tag">${blog.category || '未分类'}</span>
                                    <p><i class="fas fa-calendar-alt"></i> 创建时间: ${new Date(blog.created_at).toLocaleString()}</p>
                                    <p><i class="fas fa-edit"></i> 最后修改: ${new Date(blog.last_modified).toLocaleString()}</p>
                                </div>
                                <div class="blog-item-buttons">
                                    <button class="edit-btn" onclick="editBlog('${blog.filename}')">
                                        <i class="fas fa-pencil-alt"></i> 编辑
                                    </button>
                                    <button class="delete-btn" onclick="deleteBlog('${blog.filename}')">
                                        <i class="fas fa-trash-alt"></i> 删除
                                    </button>
                                </div>
                            `;
                            blogList.appendChild(blogItem);
                        });
                    } else {
                            console.log('No blogs found:', data.message);
                            const blogList = document.getElementById('blogList');
                            if (blogList) {
                                blogList.innerHTML = `
                                    <div class="empty-blog-list">
                                        <i class="fas fa-pencil-alt"></i>
                                        <p>还没有博客呢，赶紧发表自己的第一篇博客吧！</p>
                                        <button onclick="document.getElementById('content').focus()" class="start-writing-btn">
                                            <i class="fas fa-edit"></i> 开始写作
                                        </button>
                                    </div>
                                `;
                            }
                        }
                })
                .catch(error => {
                    console.error('Error loading blogs:', error);
                    const blogList = document.getElementById('blogList');
                    if (blogList) {
                        blogList.innerHTML = '<p class="error-message">加载博客列表时发生错误</p>';
                    }
                });
        }




        // 标签页切换功能
        function switchTab(tab) {
            const editTab = document.getElementById('editTab');
            const previewTab = document.getElementById('previewTab');
            const splitTab = document.getElementById('splitTab');
            const editorContent = document.querySelector('.editor-content');
            const editorPanel = document.getElementById('editorPanel');
            const previewPanel = document.getElementById('previewPanel');

            // 移除所有活动状态
            [editTab, previewTab, splitTab].forEach(btn => btn.classList.remove('active'));

            // 重置所有面板的显示状态
            editorPanel.style.display = 'flex';
            previewPanel.style.display = 'none';

            // 根据选择的标签页设置样式
            switch(tab) {
                case 'edit':
                    editTab.classList.add('active');
                    editorContent.className = 'editor-content';
                    editorPanel.style.display = 'flex';
                    previewPanel.style.display = 'none';
                    break;
                case 'preview':
                    previewTab.classList.add('active');
                    editorContent.className = 'editor-content';
                    editorPanel.style.display = 'none';
                    previewPanel.style.display = 'block';
                    updatePreview();
                    break;
                case 'split':
                    splitTab.classList.add('active');
                    editorContent.className = 'editor-content split-view';
                    editorPanel.style.display = 'flex';
                    previewPanel.style.display = 'block';
                    updatePreview();
                    break;
            }
        }

        // 实时预览更新
        function updatePreview() {
            const title = document.getElementById('title').value;
            const content = document.getElementById('content').value;
            const compiledContent = document.getElementById('compiledContent');

            if (!title && !content) {
                compiledContent.innerHTML = '<div class="preview-placeholder">开始输入内容以查看预览...</div>';
                return;
            }

            let fullContent = `# ${title}\n\n${content}`;

            try {
                // 预处理 LaTeX 公式，参考 view_blog.html 的处理方式
                let processedContent = fullContent;

                // 处理 eqnarray* 环境（无编号） - 转换为 align* 环境
                processedContent = processedContent.replace(/\\begin\{eqnarray\*\}([\s\S]*?)\\end\{eqnarray\*\}/g, (match, p1) => {
                    // 处理 eqnarray* 内容：移除多余的 & 符号，保持对齐结构
                    let alignContent = p1
                        .replace(/\*/g, '\\ast')  // 避免被 Markdown 解析为斜体
                        .replace(/&=/g, '&=')     // 保持等号对齐
                        .replace(/&\s*&/g, '&')   // 移除多余的 & 符号
                        .trim();

                    return `<span class="math-display">$$\\begin{align*}${alignContent}\\end{align*}$$</span>`;
                });

                // 处理 eqnarray 环境（带编号） - 转换为 align 环境
                processedContent = processedContent.replace(/\\begin\{eqnarray\}([\s\S]*?)\\end\{eqnarray\}/g, (match, p1) => {
                    // 处理 eqnarray 内容：移除多余的 & 符号，保持对齐结构
                    let alignContent = p1
                        .replace(/\*/g, '\\ast')  // 避免被 Markdown 解析为斜体
                        .replace(/&=/g, '&=')     // 保持等号对齐
                        .replace(/&\s*&/g, '&')   // 移除多余的 & 符号
                        .trim();

                    return `<span class="math-display">$$\\begin{align}${alignContent}\\end{align}$$</span>`;
                });

                // 处理 LaTeX 格式的行内公式 \(...\) -> $...$
                processedContent = processedContent.replace(/\\\(([\s\S]*?)\\\)/g, (match, p1) => {
                    // 将 * 替换为 \ast，避免被 Markdown 解析为斜体
                    const processedFormula = p1.replace(/\*/g, '\\ast');
                    return `<span class="math-inline">$${processedFormula}$</span>`;
                });

                // 处理 LaTeX 格式的块级公式 \[...\] -> $$...$$
                processedContent = processedContent.replace(/\\\[([\s\S]*?)\\\]/g, (match, p1) => {
                    const processedFormula = p1.replace(/\*/g, '\\ast');
                    return `<span class="math-display">$$${processedFormula}$$</span>`;
                });

                // 使用marked.js进行Markdown编译
                let html = marked.parse(processedContent);

                // 处理引用块样式
                html = html.replace(/<blockquote>/g, '<blockquote style="border-left: 4px solid #667eea !important; margin: 1.5em 0 !important; padding: 1em 1.5em !important; background: #f3f4f6 !important; border-radius: 0 8px 8px 0 !important; font-style: italic !important; color: #4b5563 !important;">');

                compiledContent.innerHTML = html;

                // 使用 KaTeX 渲染数学公式，参考 view_blog.html 的配置
                if (typeof renderMathInElement !== 'undefined') {
                    renderMathInElement(compiledContent, {
                        delimiters: [
                            {left: "$$", right: "$$", display: true},
                            {left: "$", right: "$", display: false},
                            {left: "\\[", right: "\\]", display: true},
                            {left: "\\(", right: "\\)", display: false}
                        ],
                        throwOnError: false,
                        output: 'htmlAndMathml',
                        trust: true,
                        strict: false,
                        fleqn: true,
                        macros: {
                            "\\eqnarray": "\\begin{align}",
                            "\\endeqnarray": "\\end{align}",
                            "\\eqnarray*": "\\begin{align*}",
                            "\\endeqnarray*": "\\end{align*}"
                        }
                    });
                }

                // 处理 mermaid 图表
                if (typeof mermaid !== 'undefined') {
                    try {
                        // 查找所有 mermaid 元素
                        const mermaidElements = compiledContent.querySelectorAll('.mermaid');
                        mermaidElements.forEach((element, index) => {
                            // 为每个 mermaid 元素创建唯一 ID
                            const id = `mermaid-${Date.now()}-${index}`;
                            element.id = id;

                            // 重新渲染 mermaid 图表
                            mermaid.render(id + '-svg', element.textContent).then(({svg}) => {
                                element.innerHTML = svg;
                            }).catch(error => {
                                console.error('Mermaid render error:', error);
                                element.innerHTML = `<div style="color: red;">Mermaid 图表渲染错误: ${error.message}</div>`;
                            });
                        });
                    } catch (error) {
                        console.error('Mermaid processing error:', error);
                    }
                }

                // 重新高亮代码
                if (typeof hljs !== 'undefined') {
                    compiledContent.querySelectorAll('pre code').forEach((block) => {
                        hljs.highlightElement(block);
                    });
                }

            } catch (error) {
                console.error('Preview error:', error);
                compiledContent.innerHTML = `<div style="color: red;">预览错误: ${error.message}</div>`;
            }
        }

        // 字数统计
        function updateWordCount() {
            const content = document.getElementById('content').value;
            const title = document.getElementById('title').value;
            const totalChars = (title + content).length;
            const wordCount = content.split(/\s+/).filter(word => word.length > 0).length;

            document.getElementById('wordCount').textContent = `字数: ${totalChars} | 词数: ${wordCount}`;
        }

        // 自动保存功能
        let autoSaveTimer;
        let lastSavedContent = '';
        let debounceTimer;

        function startAutoSave() {
            const autoSaveStatus = document.getElementById('autoSaveStatus');

            function autoSave() {
                const title = document.getElementById('title').value;
                const content = document.getElementById('content').value;
                const currentContent = title + content;

                if (currentContent !== lastSavedContent && currentContent.trim() !== '') {
                    autoSaveStatus.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 正在保存...';
                    autoSaveStatus.className = 'auto-save-status saving';

                    // 调用真实的保存草稿 API
                    fetch('/api/save_draft', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            user: user,
                            title: title,
                            content: content
                        }),
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            lastSavedContent = currentContent;
                            autoSaveStatus.innerHTML = '<i class="fas fa-check"></i> 已自动保存';
                            autoSaveStatus.className = 'auto-save-status';

                            setTimeout(() => {
                                autoSaveStatus.innerHTML = '<i class="fas fa-save"></i> 自动保存已开启';
                            }, 2000);
                        } else {
                            autoSaveStatus.innerHTML = '<i class="fas fa-exclamation-triangle"></i> 保存失败';
                            autoSaveStatus.className = 'auto-save-status saving';

                            setTimeout(() => {
                                autoSaveStatus.innerHTML = '<i class="fas fa-save"></i> 自动保存已开启';
                                autoSaveStatus.className = 'auto-save-status';
                            }, 3000);
                        }
                    })
                    .catch(error => {
                        console.error('Auto save error:', error);
                        autoSaveStatus.innerHTML = '<i class="fas fa-exclamation-triangle"></i> 保存失败';
                        autoSaveStatus.className = 'auto-save-status saving';

                        setTimeout(() => {
                            autoSaveStatus.innerHTML = '<i class="fas fa-save"></i> 自动保存已开启';
                            autoSaveStatus.className = 'auto-save-status';
                        }, 3000);
                    });
                }
            }

            // 每30秒自动保存一次
            autoSaveTimer = setInterval(autoSave, 30000);
        }

        // 防抖自动保存（用户输入时触发）
        function debouncedAutoSave() {
            clearTimeout(debounceTimer);
            debounceTimer = setTimeout(() => {
                const title = document.getElementById('title').value;
                const content = document.getElementById('content').value;
                const currentContent = title + content;
                const autoSaveStatus = document.getElementById('autoSaveStatus');

                if (currentContent !== lastSavedContent && currentContent.trim() !== '') {
                    autoSaveStatus.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 正在保存...';
                    autoSaveStatus.className = 'auto-save-status saving';

                    fetch('/api/save_draft', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            user: user,
                            title: title,
                            content: content
                        }),
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            lastSavedContent = currentContent;
                            autoSaveStatus.innerHTML = '<i class="fas fa-check"></i> 已自动保存';
                            autoSaveStatus.className = 'auto-save-status';

                            setTimeout(() => {
                                autoSaveStatus.innerHTML = '<i class="fas fa-save"></i> 自动保存已开启';
                            }, 2000);
                        }
                    })
                    .catch(error => {
                        console.error('Debounced auto save error:', error);
                    });
                }
            }, 5000); // 5秒后保存
        }

        // 初始化工具栏功能
        function initializeToolbar() {
            // 创建表格选择器
            const tableSelector = document.createElement('div');
            tableSelector.className = 'table-selector';
            for (let i = 0; i < 8; i++) {
                const row = document.createElement('div');
                row.className = 'table-selector-row';
                for (let j = 0; j < 8; j++) {
                    const cell = document.createElement('div');
                    cell.className = 'table-selector-cell';
                    cell.dataset.row = i + 1;
                    cell.dataset.col = j + 1;
                    row.appendChild(cell);
                }
                tableSelector.appendChild(row);
            }
            document.body.appendChild(tableSelector);

            // 工具栏点击事件
            document.getElementById('markdown-toolbar').addEventListener('click', function(e) {
                const button = e.target.closest('.toolbar-btn');
                if (button) {
                    const action = button.getAttribute('data-action') || button.dataset.action;
                    const buttonId = button.id;

                    // 处理特殊按钮（有ID的按钮）
                    if (buttonId === 'color-dropdown') {
                        // 颜色下拉菜单的点击事件会被其自己的事件监听器处理
                        // 阻止这里的处理，避免冲突
                        return;
                    } else if (buttonId === 'math-dropdown') {
                        // 数学公式下拉菜单的点击事件会被其自己的事件监听器处理
                        // 阻止这里的处理，避免冲突
                        return;
                    } else if (buttonId === 'textBoxBtn') {
                        // 显示颜色选择器让用户选择文本框背景色
                        const colorPicker = document.getElementById('colorPicker');
                        const rect = button.getBoundingClientRect();

                        colorPicker.style.display = 'grid';
                        colorPicker.style.position = 'fixed';
                        colorPicker.style.top = `${rect.bottom + 5}px`;
                        colorPicker.style.left = `${rect.left}px`;
                        colorPicker.style.zIndex = '1000';
                        e.stopPropagation();
                        return;
                    }

                    // 处理有 data-action 的按钮
                    if (action === 'attachment') {
                        document.getElementById('attachmentInput').click();
                    } else if (action === 'upload-video') {
                        document.getElementById('videoUploadModal').style.display = 'block';
                    } else if (action === 'image') {
                        document.getElementById('imageUpload').click();
                    } else if (action === 'table') {
                        const rect = button.getBoundingClientRect();
                        tableSelector.style.display = 'block';
                        tableSelector.style.top = `${rect.bottom + window.scrollY}px`;
                        tableSelector.style.left = `${rect.left + window.scrollX}px`;
                        e.stopPropagation();
                    } else if (action) {
                        insertMarkdown(action);
                    }
                }
            });

            // 表格选择器事件
            tableSelector.addEventListener('mouseover', function(e) {
                if (e.target.classList.contains('table-selector-cell')) {
                    const row = parseInt(e.target.dataset.row);
                    const col = parseInt(e.target.dataset.col);
                    tableSelector.querySelectorAll('.table-selector-cell').forEach(cell => {
                        const cellRow = parseInt(cell.dataset.row);
                        const cellCol = parseInt(cell.dataset.col);
                        if (cellRow <= row && cellCol <= col) {
                            cell.classList.add('selected');
                        } else {
                            cell.classList.remove('selected');
                        }
                    });
                }
            });

            tableSelector.addEventListener('click', function(e) {
                if (e.target.classList.contains('table-selector-cell')) {
                    const rows = parseInt(e.target.dataset.row);
                    const cols = parseInt(e.target.dataset.col);
                    insertTable(rows, cols);
                    tableSelector.style.display = 'none';
                }
            });

            // 点击其他地方关闭表格选择器
            document.addEventListener('click', function() {
                tableSelector.style.display = 'none';
            });

            // 插入表格函数
            function insertTable(rows, cols) {
                const contentTextarea = document.getElementById('content');
                const start = contentTextarea.selectionStart;
                const end = contentTextarea.selectionEnd;
                const scrollTop = contentTextarea.scrollTop;

                let tableHtml = `<div style="text-align: center;">
                    <p style="font-weight: bold;">表1：默认表格标题</p>
                    <table style="border-collapse: collapse; width: 50%; margin: 0 auto;">
                    `;
                for (let i = 0; i < rows; i++) {
                    tableHtml += '  <tr>\n';
                    for (let j = 0; j < cols; j++) {
                        const cellType = i === 0 ? 'th' : 'td';
                        const cellContent = i === 0 ? `列${j+1}` : `内容`;
                        tableHtml += `    <${cellType} style="border: 1px solid black; padding: 8px; text-align: center;">${cellContent}</${cellType}>\n`;
                    }
                    tableHtml += '  </tr>\n';
                }
                tableHtml += '</table>\n</div>\n';

                contentTextarea.value = contentTextarea.value.substring(0, start) + tableHtml + contentTextarea.value.substring(end);
                contentTextarea.focus();
                contentTextarea.selectionStart = start + tableHtml.length;
                contentTextarea.selectionEnd = start + tableHtml.length;
                contentTextarea.scrollTop = scrollTop;
            }

            // Markdown插入函数
            function insertMarkdown(action) {
                const contentTextarea = document.getElementById('content');
                const start = contentTextarea.selectionStart;
                const end = contentTextarea.selectionEnd;
                const scrollTop = contentTextarea.scrollTop;
                const selectedText = contentTextarea.value.substring(start, end);
                let replacement = '';

                switch (action) {
                    case 'toc':
                        // 检查是否已经存在TOC
                        if (contentTextarea.value.includes('@[TOC]')) {
                            alert('目录已存在，无需重复插入');
                            return;
                        }
                        replacement = '@[TOC](这里写目录标题)\n\n';
                        contentTextarea.value = replacement + contentTextarea.value;
                        contentTextarea.setSelectionRange(9, 17);
                        contentTextarea.focus();
                        return;
                    case 'bold':
                        replacement = `**${selectedText || '加粗文本'}**`;
                        break;
                    case 'italic':
                        replacement = `*${selectedText || '斜体文本'}*`;
                        break;
                    case 'heading':
                        // 如果有选中文本，在行首添加 ##
                        if (selectedText) {
                            replacement = `## ${selectedText}`;
                        } else {
                            // 如果没有选中文本，在当前行首添加 ##
                            const lineStart = contentTextarea.value.lastIndexOf('\n', start - 1) + 1;
                            const lineEnd = contentTextarea.value.indexOf('\n', start);
                            const currentLine = contentTextarea.value.substring(lineStart, lineEnd === -1 ? contentTextarea.value.length : lineEnd);

                            if (currentLine.trim() === '') {
                                replacement = '## 标题';
                            } else {
                                // 在当前行前面添加 ##
                                contentTextarea.value = contentTextarea.value.substring(0, lineStart) +
                                                      `## ${currentLine}` +
                                                      contentTextarea.value.substring(lineEnd === -1 ? contentTextarea.value.length : lineEnd);
                                contentTextarea.focus();
                                contentTextarea.selectionStart = lineStart + 3;
                                contentTextarea.selectionEnd = lineStart + 3 + currentLine.length;
                                contentTextarea.scrollTop = scrollTop;
                                return;
                            }
                        }
                        break;
                    case 'strikethrough':
                        replacement = `~~${selectedText || '删除线文本'}~~`;
                        break;
                    case 'unordered-list':
                        replacement = `\n- ${selectedText || '列表项'}`;
                        break;
                    case 'ordered-list':
                        replacement = `\n1. ${selectedText || '列表项'}`;
                        break;
                    case 'task-list':
                        replacement = `\n- [ ] ${selectedText || '待办事项'}`;
                        break;
                    case 'blockquote':
                        replacement = `> ${selectedText || '引用文本'}`;
                        break;
                    case 'code-block':
                        replacement = `\n\`\`\`\n${selectedText || '在这里插入代码'}\n\`\`\`\n`;
                        break;
                    case 'link':
                        replacement = `[${selectedText || '链接文本'}](https://example.com)`;
                        break;
                    case 'flowchart':
                        replacement = `<div class="mermaid">
                        flowchart TD
                        st([开始]) --> op[我的操作]
                        op --> cond{确认？}
                        cond -->|是| e([结束])
                        cond -->|否| op
                        </div>`;
                        break;
                }

                contentTextarea.value = contentTextarea.value.substring(0, start) + replacement + contentTextarea.value.substring(end);
                contentTextarea.focus();
                contentTextarea.selectionStart = start;
                contentTextarea.selectionEnd = start + replacement.length;
                contentTextarea.scrollTop = scrollTop;
            }
        }

        // 在页面加载完成时初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('DOM Content Loaded');

            if (!user || !key || !token) {
                console.error('Missing required parameters');
                alert('缺少必要的用户信息');
                return;
            }

            console.log('Loading blog data...');
            loadBlogCategories();
            loadUserBlogs();
            loadDraft();

            // 初始化新功能
            startAutoSave();
            initializeColorDropdown();
            initializeMathDropdown();
            initializeToolbar();
            initializeTextBoxModal();

            // 添加内容变化监听器
            const titleInput = document.getElementById('title');
            const contentTextarea = document.getElementById('content');

            [titleInput, contentTextarea].forEach(element => {
                element.addEventListener('input', () => {
                    updateWordCount();
                    // 触发防抖自动保存
                    debouncedAutoSave();
                    // 如果当前在分屏或预览模式，自动更新预览
                    const editorContent = document.querySelector('.editor-content');
                    if (editorContent.classList.contains('split-view') ||
                        document.getElementById('previewPanel').style.display === 'block') {
                        updatePreview();
                    }
                });
            });

            // 初始化字数统计
            updateWordCount();

            // 默认显示编辑模式
            switchTab('edit');

            // 初始化上传次数显示
            updateRemainingUploads();
        });


        // 编辑博客
        function editBlog(filename) {
            const editButton = event.target;
            editButton.disabled = true;
            editButton.textContent = '编辑中...';

            currentEditingFilename = filename;
            
            // 首先加载所有分类
            fetch(`/api/blog/categories?user=${encodeURIComponent(user)}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        const categorySelect = document.getElementById('blogCategory');
                        // 清空现有选项，保留默认选项
                        categorySelect.innerHTML = '<option value="" disabled>请选择分类</option>';
                        data.categories.forEach(category => {
                            const option = document.createElement('option');
                            option.value = category;
                            option.textContent = category;
                            categorySelect.appendChild(option);
                        });
                        
                        // 加载博客内容
                        return fetch(`/api/get_blog_content/${filename}`);
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        const content = data.content;
                        const lines = content.split('\n');
                        const title = lines[0].replace('# ', '');
                        const body = lines.slice(2).join('\n');
                        
                        // 设置标题和内容
                        document.getElementById('title').value = title;
                        document.getElementById('content').value = body;
                        
                        // 设置分类
                        if (data.category) {
                            document.getElementById('blogCategory').value = data.category;
                        }
                        
                        document.getElementById('publish').style.display = 'none';
                        document.getElementById('update').style.display = 'inline-block';
                        document.getElementById('cancel').style.display = 'inline-block';

                        // 加载并显示知识图谱
                        loadGraph(filename.split('.')[0] + '.json');
                    } else {
                        throw new Error(data.message);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert(`加载博客内容失败：${error.message}`);
                })
                .finally(() => {
                    editButton.disabled = false;
                    editButton.textContent = '编辑';
                });
        }


            function loadGraph(graphFilename) {
            fetch('/api/graph/' + graphFilename)
                .then(response => {
                    if (!response.ok) {
                        if (response.status === 404) {
                            console.log('Graph file not found');
                            document.getElementById('codeDisplay').value = ''; // 清空文本区域
                            return null;
                        }
                        throw new Error('HTTP error! status: ' + response.status);
                    }
                    return response.json();
                })
                .then(graphData => {
                    if (graphData) {
                        updateCodeDisplay(graphData);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    document.getElementById('codeDisplay').value = ''; // 出错时清空文本区域
                });
        }

        function updateCodeDisplay(graphData) {
            const codeDisplay = document.getElementById('codeDisplay');
            let codeContent = '';

            graphData.links.forEach(link => {
                const source = graphData.nodes.find(node => node.id === link.source)?.name || link.source;
                const target = graphData.nodes.find(node => node.id === link.target)?.name || link.target;
                const value = link.value || '';
                codeContent += `${source},${target},{${value}}\n`;  // 修改这里，将关系用大括号括起来
            });

            codeDisplay.value = codeContent.trim();
        }





    

    // 修改重置编辑器函数
    function resetEditor() {
        document.getElementById('title').value = '';
        document.getElementById('content').value = '';
        document.getElementById('publish').style.display = 'inline-block';
        document.getElementById('update').style.display = 'none';
        document.getElementById('cancel').style.display = 'none';
        currentEditingFilename = null;

        // 确保更新按钮恢复到初始状态
        const updateButton = document.getElementById('update');
        updateButton.disabled = false;
        updateButton.textContent = '更新博客';
    }


        // 取消编辑
        document.getElementById('cancel').addEventListener('click', resetEditor);

        function resetEditor() {
            document.getElementById('title').value = '';
            document.getElementById('content').value = '';
            document.getElementById('publish').style.display = 'inline-block';
            document.getElementById('update').style.display = 'none';
            document.getElementById('cancel').style.display = 'none';
            currentEditingFilename = null;
        }

        // 添加删除博客的函数
        function deleteBlog(filename) {
            if (confirm(`确定要删除博客 "${filename}" 吗？`)) {
                fetch('/api/delete_blog', {  // 更新这里的 URL
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ 
                        user: user,
                        blog_user: user,
                        key: key,   
                        token: token,
                        filename: filename
                    }),
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert('博客删除成功！');
                        loadUserBlogs();  // 重新加载博客列表
                    } else {
                        alert(`博客删除失败，请重试。错误信息: ${data.message}`);
                    }
                })
                .catch((error) => {
                    console.error('Error:', error);
                    alert('发生错误，请重试。');
                });
            }
        }

        // 保存草稿
        document.getElementById('saveDraft').addEventListener('click', function() {
            const title = document.getElementById('title').value;
            const content = document.getElementById('content').value;

            fetch('/api/save_draft', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ 
                    user: user,
                    title: title,
                    content: content
                }),
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    document.getElementById('draftStatus').textContent = '草稿已保存';
                    setTimeout(() => {
                        document.getElementById('draftStatus').textContent = '';
                    }, 3000);
                } else {
                    alert(`保存草稿失败，请重试。错误信息: ${data.message}`);
                }
            })
            .catch((error) => {
                console.error('Error:', error);
                alert('发生错误，请重试。');
            });
        });

        // 加载草稿
        function loadDraft() {
            fetch(`/api/get_draft/${user}`)
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    document.getElementById('title').value = data.title;
                    document.getElementById('content').value = data.content;
                }
            })
            .catch(error => console.error('Error:', error));
        }

        // 页面加载时获取用户博客列表和草稿
        // window.onload = function() {
        //     loadUserBlogs();
        //     loadDraft();
        // };

        let isCompiled = false;

        // 重复的工具栏事件监听器已移除，使用 initializeToolbar 中的版本





        // 编译功能已移除，预览功能集成到标签页中

        // 这些事件监听器已移到各自的初始化函数中

        // 初始化数学公式下拉菜单
        function initializeMathDropdown() {
            const mathDropdownBtn = document.getElementById('math-dropdown');
            const globalMathDropdown = document.getElementById('global-math-dropdown');

            if (!mathDropdownBtn || !globalMathDropdown) {
                console.error('Math dropdown elements not found!');
                return;
            }

            // 只生成一次数学公式选项
            if (globalMathDropdown.children.length === 0) {
                // 添加标题
                const title = document.createElement('div');
                title.style.cssText = `
                    width: 100%;
                    text-align: center;
                    font-weight: bold;
                    color: var(--primary-color);
                    margin-bottom: 10px;
                    padding-bottom: 8px;
                    border-bottom: 1px solid var(--border-color);
                    font-size: 14px;
                `;
                title.textContent = '数学公式';
                globalMathDropdown.appendChild(title);

                const formulas = [
                    { formula: '\\sum_{i=1}^n', title: '求和', symbol: '∑' },
                    { formula: '\\prod_{i=1}^n', title: '求积', symbol: '∏' },
                    { formula: '\\int_{a}^b', title: '积分', symbol: '∫' },
                    { formula: '\\frac{a}{b}', title: '分数', symbol: 'a/b' },
                    { formula: '\\sqrt{x}', title: '平方根', symbol: '√' },
                    { formula: 'x^{y}', title: '上标', symbol: 'x^y' },
                    { formula: 'x_{y}', title: '下标', symbol: 'x_y' },
                    { formula: '\\begin{bmatrix} a&b\\\\c&d\\end{bmatrix}', title: '矩阵', symbol: '[ ]' },
                    { formula: '\\begin{eqnarray}\na &= b \\\\\nc &= d\n\\end{eqnarray}', title: '公式组', symbol: 'eqn' },
                    { formula: '\\lim_{x \\to \\infty}', title: '极限', symbol: 'lim' },
                    { formula: '\\sin(x)', title: '正弦', symbol: 'sin' },
                    { formula: '\\cos(x)', title: '余弦', symbol: 'cos' },
                    { formula: '\\tan(x)', title: '正切', symbol: 'tan' },
                    { formula: '\\alpha', title: 'alpha', symbol: 'α' },
                    { formula: '\\beta', title: 'beta', symbol: 'β' },
                    { formula: '\\gamma', title: 'gamma', symbol: 'γ' },
                    { formula: '\\delta', title: 'delta', symbol: 'δ' },
                    { formula: '\\epsilon', title: 'epsilon', symbol: 'ε' },
                    { formula: '\\theta', title: 'theta', symbol: 'θ' },
                    { formula: '\\lambda', title: 'lambda', symbol: 'λ' },
                    { formula: '\\mu', title: 'mu', symbol: 'μ' },
                    { formula: '\\pi', title: 'pi', symbol: 'π' },
                    { formula: '\\sigma', title: 'sigma', symbol: 'σ' },
                    { formula: '\\phi', title: 'phi', symbol: 'φ' },
                    { formula: '\\omega', title: 'omega', symbol: 'ω' },
                    { formula: '\\infty', title: '无穷', symbol: '∞' },
                    { formula: '\\partial', title: '偏导数', symbol: '∂' },
                    { formula: '\\nabla', title: '梯度', symbol: '∇' },
                    { formula: '\\pm', title: '正负', symbol: '±' },
                    { formula: '\\times', title: '乘', symbol: '×' },
                    { formula: '\\div', title: '除', symbol: '÷' },
                    { formula: '\\neq', title: '不等于', symbol: '≠' },
                    { formula: '\\leq', title: '小于等于', symbol: '≤' },
                    { formula: '\\geq', title: '大于等于', symbol: '≥' },
                    { formula: '\\approx', title: '约等于', symbol: '≈' },
                    { formula: '\\equiv', title: '恒等于', symbol: '≡' },
                    { formula: '\\in', title: '属于', symbol: '∈' }
                ];

                formulas.forEach(item => {
                    const formulaButton = document.createElement('button');
                    formulaButton.setAttribute('data-formula', item.formula);
                    formulaButton.setAttribute('title', item.title);
                    formulaButton.textContent = item.symbol;
                    globalMathDropdown.appendChild(formulaButton);
                });
            }

            mathDropdownBtn.addEventListener('click', function(e) {
                e.stopPropagation();
                const rect = mathDropdownBtn.getBoundingClientRect();
                globalMathDropdown.style.display = 'flex';
                globalMathDropdown.classList.add('show');
                globalMathDropdown.style.position = 'fixed';
                globalMathDropdown.style.left = rect.left + 'px';
                globalMathDropdown.style.top = (rect.bottom + 5) + 'px';
                globalMathDropdown.style.zIndex = 9999;
            });

            globalMathDropdown.addEventListener('click', function(e) {
                e.stopPropagation();
                if (e.target.tagName === 'BUTTON') {
                    const formula = e.target.getAttribute('data-formula');
                    const contentTextarea = document.getElementById('content');
                    const start = contentTextarea.selectionStart;
                    const end = contentTextarea.selectionEnd;
                    const scrollTop = contentTextarea.scrollTop;
                    const replacement = `$$${formula}$$`;

                    contentTextarea.value = contentTextarea.value.substring(0, start) + replacement + contentTextarea.value.substring(end);
                    contentTextarea.focus();
                    contentTextarea.selectionStart = start + replacement.length;
                    contentTextarea.selectionEnd = start + replacement.length;
                    contentTextarea.scrollTop = scrollTop;

                    globalMathDropdown.style.display = 'none';
                    globalMathDropdown.classList.remove('show');
                }
            });

            document.addEventListener('click', function() {
                globalMathDropdown.style.display = 'none';
                globalMathDropdown.classList.remove('show');
            });
        }



        // 图片上传事件监听器（保留这个，因为它在工具栏初始化中被引用）
        document.getElementById('imageUpload').addEventListener('change', function(e) {
            const file = e.target.files[0];
            if (file) {
                if (file.size > 2 * 1024 * 1024) { // 2MB = 2 * 1024 * 1024 bytes
                    alert('图片大小不能超过2MB');
                    this.value = ''; // 清空文件输入，允许用户重新选择
                    return;
                }

                const formData = new FormData();
                formData.append('image', file);
                formData.append('user', user); // 添加用户信息

                const contentTextarea = document.getElementById('content');
                const cursorPosition = contentTextarea.selectionStart;

                // 显示上传进度（可选）
                const progressIndicator = document.createElement('span');
                progressIndicator.textContent = '图片上传中...';
                progressIndicator.style.color = 'gray';
                progressIndicator.style.fontSize = '0.8em';
                contentTextarea.parentNode.insertBefore(progressIndicator, contentTextarea.nextSibling);

                fetch('/api/uploadimage', {
                    method: 'POST',
                    body: formData
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        const imageUrl = data.imageUrl;
                        const fileName = imageUrl.split('/').pop(); // 获取文件名
                        const altText = fileName.split('.')[0]; // 使用文件名（不包括扩展名）作为 alt 文本
                        const imageHtml = `<img src="${imageUrl}" width="800" alt="${altText}" style="display: block; text-align: left; margin-left: 0; margin-right: auto;">`;                        
                        updateRemainingUploads();
                        // 在光标位置插入图片 HTML
                        contentTextarea.value = contentTextarea.value.substring(0, cursorPosition) + 
                                                imageHtml + 
                                                contentTextarea.value.substring(cursorPosition);
                        
                        // 将光标移动到插入的 HTML 之后
                        const newCursorPosition = cursorPosition + imageHtml.length;
                        contentTextarea.setSelectionRange(newCursorPosition, newCursorPosition);
                        contentTextarea.focus();
                    } else {
                        alert('图片上传失败：' + data.message);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('图片上传出错，请重试');
                })
                .finally(() => {
                    // 移除进度指示器
                    if (progressIndicator) {
                        progressIndicator.remove();
                    }
                });
            }
        });




        function insertTextAtCursor(textarea, text) {
            const start = textarea.selectionStart;
            const end = textarea.selectionEnd;
            const value = textarea.value;
            textarea.value = value.substring(0, start) + text + value.substring(end);
            textarea.selectionStart = textarea.selectionEnd = start + text.length;
            textarea.focus();
        }



        // 初始化颜色下拉菜单
        function initializeColorDropdown() {
            const colorDropdownBtn = document.getElementById('color-dropdown');
            const globalColorDropdown = document.getElementById('global-color-dropdown');

            if (!colorDropdownBtn || !globalColorDropdown) {
                console.error('Color dropdown elements not found!');
                return;
            }

            // 只生成一次颜色选项
            if (globalColorDropdown.children.length === 0) {
                // 添加标题
                const title = document.createElement('div');
                title.style.cssText = `
                    grid-column: 1 / -1;
                    text-align: center;
                    font-weight: bold;
                    color: var(--primary-color);
                    margin-bottom: 8px;
                    padding-bottom: 6px;
                    border-bottom: 1px solid var(--border-color);
                    font-size: 14px;
                `;
                title.textContent = '字体颜色';
                globalColorDropdown.appendChild(title);

                const colors = [
                    { name: '黑色', value: '#000000' },
                    { name: '深灰色', value: '#808080' },
                    { name: '灰色', value: '#A9A9A9' },
                    { name: '银色', value: '#C0C0C0' },
                    { name: '红色', value: '#FF0000' },
                    { name: '栗色', value: '#800000' },
                    { name: '黄色', value: '#FFFF00' },
                    { name: '橄榄色', value: '#808000' },
                    { name: '绿色', value: '#008000' },
                    { name: '青色', value: '#00FFFF' },
                    { name: '蓝色', value: '#0000FF' },
                    { name: '海军蓝', value: '#000080' },
                    { name: '紫色', value: '#800080' },
                    { name: '粉红色', value: '#FFC0CB' },
                    { name: '橙色', value: '#FFA500' },
                    { name: '棕色', value: '#A52A2A' }
                ];
                colors.forEach(color => {
                    const colorOption = document.createElement('div');
                    colorOption.className = 'color-option';
                    colorOption.style.backgroundColor = color.value;
                    colorOption.setAttribute('data-color', color.value);
                    colorOption.setAttribute('title', color.name);
                    globalColorDropdown.appendChild(colorOption);
                });
            }

            colorDropdownBtn.addEventListener('click', function(e) {
                e.stopPropagation();
                const rect = colorDropdownBtn.getBoundingClientRect();
                globalColorDropdown.style.display = 'grid';
                globalColorDropdown.classList.add('show');
                globalColorDropdown.style.position = 'fixed';
                globalColorDropdown.style.left = rect.left + 'px';
                globalColorDropdown.style.top = (rect.bottom + 5) + 'px';
                globalColorDropdown.style.zIndex = 9999;
            });

            globalColorDropdown.addEventListener('click', function(e) {
                e.stopPropagation();
                if (e.target.classList.contains('color-option')) {
                    const color = e.target.getAttribute('data-color');
                    const contentTextarea = document.getElementById('content');
                    const start = contentTextarea.selectionStart;
                    const end = contentTextarea.selectionEnd;
                    const scrollTop = contentTextarea.scrollTop;
                    const selectedText = contentTextarea.value.substring(start, end);
                    const replacement = `<font color="${color}">${selectedText || '彩色文本'}</font>`;
                    contentTextarea.value = contentTextarea.value.substring(0, start) + replacement + contentTextarea.value.substring(end);
                    contentTextarea.focus();
                    contentTextarea.selectionStart = start + replacement.length;
                    contentTextarea.selectionEnd = start + replacement.length;
                    contentTextarea.scrollTop = scrollTop;
                    globalColorDropdown.style.display = 'none';
                    globalColorDropdown.classList.remove('show');
                }
            });

            document.addEventListener('click', function() {
                globalColorDropdown.style.display = 'none';
                globalColorDropdown.classList.remove('show');
            });
        }

        // 初始化文本框模态框
        function initializeTextBoxModal() {
            const colorPicker = document.getElementById('colorPicker');
            const textBoxModal = document.getElementById('textBoxModal');

            // 创建颜色选项
            const colors = [
                '#FF6B6B', '#4ECDC4', '#45B7D1', '#FFA07A', '#98D8C8',
                '#F7DC6F', '#BB8FCE', '#85C1E9', '#F8C471', '#82E0AA',
                '#F1948A', '#85C1E9', '#F4D03F', '#A569BD', '#5DADE2'
            ];

            colorPicker.innerHTML = '';
            colors.forEach(color => {
                const colorOption = document.createElement('div');
                colorOption.className = 'color-option';
                colorOption.style.backgroundColor = color;
                colorOption.setAttribute('data-color', color);
                colorOption.title = color;
                colorPicker.appendChild(colorOption);
            });

            // 处理颜色选择
            colorPicker.addEventListener('click', function(e) {
                if (e.target.classList.contains('color-option')) {
                    const color = e.target.getAttribute('data-color');
                    document.getElementById('textBoxBackgroundColor').value = color;
                    this.style.display = 'none';
                    textBoxModal.style.display = 'block';
                    e.stopPropagation();
                }
            });

            // 点击其他区域关闭颜色选择器
            document.addEventListener('click', function(e) {
                const textBoxBtn = document.getElementById('textBoxBtn');
                if (!colorPicker.contains(e.target) && e.target !== textBoxBtn && !textBoxBtn.contains(e.target)) {
                    colorPicker.style.display = 'none';
                }
            });

            // 插入文本框
            document.getElementById('insertTextBox').addEventListener('click', function() {
                const backgroundColor = document.getElementById('textBoxBackgroundColor').value || '#f0f0f0';
                const content = document.getElementById('textBoxContent').value || '请输入内容';

                // 计算边框颜色（比背景色深一些）
                const borderColor = getDarkerColorForTextBox(backgroundColor);
                const defaultFont = 'Arial, sans-serif';

                const textBoxHtml = `<div class="text-box" style="display: inline-block; background-color: ${backgroundColor}; border-left: 8px solid ${borderColor}; border-top: 1px solid ${backgroundColor}; border-right: 1px solid ${backgroundColor}; border-bottom: 1px solid ${backgroundColor}; font-family: ${defaultFont}; padding: 10px 15px; margin: 10px 0;">${content}</div>`;

                const contentTextarea = document.getElementById('content');
                const cursorPos = contentTextarea.selectionStart;
                const textBefore = contentTextarea.value.substring(0, cursorPos);
                const textAfter = contentTextarea.value.substring(cursorPos);
                contentTextarea.value = textBefore + textBoxHtml + textAfter;

                // 清空并关闭模态框
                document.getElementById('textBoxContent').value = '';
                document.getElementById('textBoxBackgroundColor').value = '';
                textBoxModal.style.display = 'none';
            });

            // 关闭文本框模态框
            textBoxModal.querySelector('.close').addEventListener('click', function() {
                textBoxModal.style.display = 'none';
            });

            // 点击模态框外部关闭
            textBoxModal.addEventListener('click', function(e) {
                if (e.target === textBoxModal) {
                    textBoxModal.style.display = 'none';
                }
            });
        }

        // 获取更深的颜色（用于文本框）
        function getDarkerColorForTextBox(hex) {
            // 将 hex 转换为 RGB
            let r = parseInt(hex.slice(1, 3), 16);
            let g = parseInt(hex.slice(3, 5), 16);
            let b = parseInt(hex.slice(5, 7), 16);

            // 简单地减少亮度
            r = Math.max(0, r - 50);
            g = Math.max(0, g - 50);
            b = Math.max(0, b - 50);

            // 转回 Hex
            return '#' + ((1 << 24) + (r << 16) + (g << 8) + b).toString(16).slice(1);
        }

        // 附件上传事件监听器（保留这个，因为它在工具栏初始化中被引用）
        document.getElementById('attachmentInput').addEventListener('change', function(e) {
            const file = e.target.files[0];
            if (file) {
                if (file.size > 20 * 1024 * 1024) {
                    alert('文件大小不能超过20MB');
                    return;
                }

                const formData = new FormData();
                // 使用 Blob 构造函数重命名文件，保留原始文件名
                const renamedFile = new File([file], encodeURIComponent(file.name), {type: file.type});
                formData.append('attachment', renamedFile);
                formData.append('user', user);
                formData.append('key', key);
                formData.append('token', token);
                formData.append('originalFilename', file.name);  // 添加原始文件名

                // 显示上传进度
                const progressIndicator = document.createElement('div');
                progressIndicator.textContent = '附件上传中...';
                progressIndicator.style.position = 'fixed';
                progressIndicator.style.top = '50%';
                progressIndicator.style.left = '50%';
                progressIndicator.style.transform = 'translate(-50%, -50%)';
                progressIndicator.style.padding = '10px';
                progressIndicator.style.backgroundColor = 'rgba(0,0,0,0.7)';
                progressIndicator.style.color = 'white';
                progressIndicator.style.borderRadius = '5px';
                document.body.appendChild(progressIndicator);

                fetch('/api/upload_attachment', {
                    method: 'POST',
                    body: formData
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        const attachmentLink = `[${file.name}](/api/download_attachment/${data.filename})`;
                        insertTextAtCursor(attachmentLink);
                        updateRemainingUploads();
                    } else {
                        alert('附件上传失败：' + data.message);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('附件上传出错，请重试');
                })
                .finally(() => {
                    // 移除进度指示器
                    progressIndicator.remove();    
                    // 清空文件输入，允许再次选择同一文件
                    this.value = '';     
                });
            }
        });


        // 表格处理代码已移到initializeToolbar函数中







    var chart = echarts.init(document.getElementById('garps'), 'white', {renderer: 'canvas'});
    
    var nodeColors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#FFA07A', '#98D8C8'];
    var linkColors = ['#8B0000', '#006400', '#00008B', '#8B4513', '#2F4F4F'];  // 深色的连线颜色

    function getLighterColor(color) {
        // 将颜色转换为RGB
        var r = parseInt(color.slice(1, 3), 16);
        var g = parseInt(color.slice(3, 5), 16);
        var b = parseInt(color.slice(5, 7), 16);
        
        // 使颜色变淡（这里我们增加了亮度）
        r = Math.min(255, r + 100);
        g = Math.min(255, g + 100);
        b = Math.min(255, b + 100);
        
        // 转换回十六进制
        return '#' + ((1 << 24) + (r << 16) + (g << 8) + b).toString(16).slice(1);
    }

    function getDarkerColor(color) {
        // 将颜色转换为RGB
        var r = parseInt(color.slice(1, 3), 16);
        var g = parseInt(color.slice(3, 5), 16);
        var b = parseInt(color.slice(5, 7), 16);
        
        // 使颜色变深（这里我们减少了亮度）
        r = Math.max(0, r - 50);
        g = Math.max(0, g - 50);
        b = Math.max(0, b - 50);
        
        // 转换回十六进制
        return '#' + ((1 << 24) + (r << 16) + (g << 8) + b).toString(16).slice(1);
    }

    // 添加新的辅助函数
    function getRandomColor() {
        return '#' + Math.floor(Math.random()*16777215).toString(16).padStart(6, '0');
    }

    function getComplementaryColor(hex) {
        // 将十六进制颜色转换为RGB
        var r = parseInt(hex.slice(1, 3), 16);
        var g = parseInt(hex.slice(3, 5), 16);
        var b = parseInt(hex.slice(5, 7), 16);

        // 计算补色
        r = 255 - r;
        g = 255 - g;
        b = 255 - b;

        // 转换回十六进制
        return '#' + ((1 << 24) + (r << 16) + (g << 8) + b).toString(16).slice(1);
    }

    var option = {
        backgroundColor: '#FFFAFA',
        series: [{
            type: 'graph',
            layout: 'force',
            force: {
                repulsion: [300, 700],  // 增加斥力
                edgeLength: 200,  // 增加边的长度
                gravity: 0.05,  // 添加一个小的重力效果
                layoutAnimation: true,
            },
            symbolSize: 50,  // 减小节点的默认大小
            roam: true,
            draggable: true,
            focusNodeAdjacency: true,
            label: {
                normal: {
                    show: true,
                    position: 'inside',
                    color: '#000',
                    fontSize: 14  // 减小字体大小以适应更小的节点
                }
            },
            edgeLabel: {
                normal: {
                    show: true,
                    formatter: function(params) {
                        var text = params.data.value;
                        var lines = [];
                        var maxLineLength = 20; // 每行最大字符数
                        for (var i = 0; i < text.length; i += maxLineLength) {
                            lines.push(text.substr(i, maxLineLength));
                        }
                        return lines.join('\n');
                    },
                    fontSize: 12,
                    color: '#000',
                    backgroundColor: 'rgba(255, 255, 255, 0.7)',
                    padding: [4, 8],
                    borderRadius: 4
                }
            },
            data: [],
            links: []
        }]
    };

    chart.setOption(option);

    document.getElementById('addRelationBtn').addEventListener('click', function() {
        document.getElementById('addRelationForm').style.display = 'block';
    });

    document.getElementById('cancelRelation').addEventListener('click', function() {
        document.getElementById('addRelationForm').style.display = 'none';
    }); 

    document.getElementById('submitRelation').addEventListener('click', function() {
        var source = document.getElementById('sourceInput').value.trim();
        var target = document.getElementById('targetInput').value.trim();
        var value = document.getElementById('valueInput').value.trim();

        if (source && target && value) {
            var codeDisplay = document.getElementById('codeDisplay');
            codeDisplay.value += source + ',' + target + ',{' + value + '}\n';

            // 清空输入框并隐藏表单
            document.getElementById('sourceInput').value = '';
            document.getElementById('targetInput').value = '';
            document.getElementById('valueInput').value = '';
            document.getElementById('addRelationForm').style.display = 'none';
        } else {
            alert('请填写所有字段');
        }
    });

 
            
  



    // 修改 addRelation 函数
    function addRelation(source, target, value, data, links) {
        function addOrUpdateNode(name) {
            var node = data.find(item => item.name === name);
            if (!node) {
                var backgroundColor = getRandomColor();
                var textColor = getComplementaryColor(backgroundColor);
                node = {
                    name: name, 
                    symbolSize: 50,
                    itemStyle: {
                        color: backgroundColor,
                        borderColor: getDarkerColor(backgroundColor),
                        borderWidth: 2
                    },
                    label: {
                        color: textColor,
                        fontSize: 12
                    },
                    x: Math.random() * chart.getWidth(),
                    y: Math.random() * chart.getHeight()
                };
                data.push(node);
            }
            node.symbolSize += 5;
            return node;
        }

        
        addOrUpdateNode(source);
        addOrUpdateNode(target);

        var existingLinkColor = links.find(link => link.value === value)?.lineStyle?.color;

        var linkColor = existingLinkColor || getDarkerColor(linkColors[links.length % linkColors.length]);

        links.push({
            source: source,
            target: target,
            value: value,
            lineStyle: {
                color: linkColor,
                width: 2
            },
            label: {
                show: false,
                formatter: function(params) {
                    var text = params.data.value;
                    var lines = [];
                    var maxLineLength = 20; // 每行最大字符数
                    for (var i = 0; i < text.length; i += maxLineLength) {
                        lines.push(text.substr(i, maxLineLength));
                    }
                    return lines.join('\n');
                },
                fontSize: 12,
                color: '#000',
                backgroundColor: 'rgba(255, 255, 255, 0.7)',
                padding: [4, 8],
                borderRadius: 4
            }
        });
    }




        var latestGraphData = {
        nodes: [],
        links: []
    };

        document.getElementById('generateGraphBtn').addEventListener('click', function() {
        var code = document.getElementById('codeDisplay').value;
        var lines = code.split('\n');
        
        var newData = [];
        var newLinks = [];
        
        lines.forEach(function(line) {
            var match = line.match(/^(.*?),(.*?),\{(.*?)\}$/);
            if (match) {
                addRelation(match[1].trim(), match[2].trim(), match[3].trim(), newData, newLinks);
            }
        });
        
        option.series[0].data = newData;
        option.series[0].links = newLinks;
        chart.setOption(option);

        // 更新全局变量
        latestGraphData.nodes = newData;
        latestGraphData.links = newLinks;
        console.log('Graph data updated:', latestGraphData); // 用于调试
    });



        // 加载博客分类
        function loadBlogCategories() {
            fetch(`/api/blog/categories?user=${encodeURIComponent(user)}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        const categorySelect = document.getElementById('blogCategory');
                        data.categories.forEach(category => {
                            const option = document.createElement('option');
                            option.value = category;
                            option.textContent = category;
                            categorySelect.appendChild(option);
                        });
                    }
                })
                .catch(error => console.error('Error loading categories:', error));
        }

        // 在页面加载时调用
        // document.addEventListener('DOMContentLoaded', function() {
        //     loadBlogCategories();
        // });
        // 监听分类选择变化
        document.getElementById('blogCategory').addEventListener('change', function() {
            const publishButton = document.getElementById('publish');
            publishButton.disabled = !this.value;
        });






// 修改发布博客的事件处理
document.getElementById('publish').addEventListener('click', function() {
        const publishButton = this;
        const title = document.getElementById('title').value;
        const content = document.getElementById('content').value;
        const category = document.getElementById('blogCategory').value;

        if (!category) {
            alert('请选择博客分类');
            return;
        }

        if (title.length < 5 || title.length > 100) {
            alert('标题长度必须在5到100个字之间');
            return;
        }

        // 禁用发布按钮
        publishButton.disabled = true;
        publishButton.textContent = '发布中...';

        const markdown = `# ${title}\n\n${content}`;

        fetch('/save_blog', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ 
                markdown: markdown,
                user: user,
                key: key,
                graphData: latestGraphData,
                category: category  // 添加分类信息
            }),
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                console.log('博客发布成功，准备删除草稿');
                alert(`博客发布成功！文件名: ${data.filename}`);
                document.getElementById('title').value = '';
                document.getElementById('content').value = '';
                document.getElementById('blogCategory').value = '';  // 重置分类选择
                loadUserBlogs();
                
                // 删除草稿
                return fetch(`/api/delete_draft/${user}`, {
                    method: 'POST'
                });
            } else {
                throw new Error(`博客发布失败，请重试。错误信息: ${data.message}`);
            }
        })
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            if (data.success) {
                console.log('草稿已成功删除');
                document.getElementById('draftStatus').textContent = '';
            } else {
                console.error('删除草稿失败:', data.message);
            }
        })
        .catch((error) => {
            console.error('Error:', error);
            alert('发生错误，请重试：' + error.message);
        })
        .finally(() => {
            // 无论成功还是失败，都重新启用发布按钮
            publishButton.disabled = false;
            publishButton.textContent = '发布博客';
        });
    });




        // 更新博客
        document.getElementById('update').addEventListener('click', function() {
        const updateButton = this;
        const title = document.getElementById('title').value;
        const content = document.getElementById('content').value;
        const markdown = `# ${title}\n\n${content}`;
        console.log('latestGraphData before sending:', latestGraphData);

        if (!latestGraphData || !latestGraphData.nodes || !latestGraphData.links) {
            console.warn('latestGraphData is invalid or empty');
            // 可以选择在这里停止更新过程，或者继续但不发送图谱数据
        }
        const requestBody = { 
        user: user,
        filename: currentEditingFilename,
        content: markdown,
        graphData: latestGraphData,
        category: document.getElementById('blogCategory').value
        };
        console.log('Sending request with data:', requestBody);
        // 禁用更新按钮并更改文本
        updateButton.disabled = true;
        updateButton.textContent = '更新中...';
        fetch('/api/update_blog', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
            body: JSON.stringify(requestBody),
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('博客和知识图谱更新成功！');
                resetEditor();
                loadUserBlogs();
            } else {
                throw new Error(`更新失败，请重试。错误信息: ${data.message}`);
            }
        })
        .catch((error) => {
            console.error('Error:', error);
            alert('发生错误，请重试：' + error.message);
        })
        .finally(() => {
            // 恢复更新按钮状态
            updateButton.disabled = false;
            updateButton.textContent = '更新博客';
        });
    });



    // 视频上传相关功能
    const videoUploadModal = document.getElementById('videoUploadModal');
    const closeBtn = videoUploadModal.querySelector('.close');
    const selectVideoBtn = document.getElementById('selectVideoBtn');
    const videoUpload = document.getElementById('videoUpload');
    const selectedFileName = document.getElementById('selectedFileName');
    const uploadVideoBtn = document.getElementById('uploadVideoBtn');
    const uploadProgress = document.getElementById('uploadProgress');
    const uploadPercentage = document.getElementById('uploadPercentage');
    const onlineVideoUrl = document.getElementById('onlineVideoUrl');
    const insertOnlineVideoBtn = document.getElementById('insertOnlineVideoBtn');

    // 打开模态框
    document.querySelector('[data-action="upload-video"]').addEventListener('click', () => {
        videoUploadModal.style.display = 'block';
    });

    // 关闭模态框
    closeBtn.addEventListener('click', () => {
        videoUploadModal.style.display = 'none';
    });

    // 选择视频文件
    selectVideoBtn.addEventListener('click', () => {
        videoUpload.click();
    });

    videoUpload.addEventListener('change', (e) => {
        const file = e.target.files[0];
        if (file) {
            if (file.size > 500 * 1024 * 1024) {
                alert('视频文件大小不能超过500MB');
                videoUpload.value = '';
                selectedFileName.textContent = '';
                uploadVideoBtn.disabled = true;
                return;
            }
            selectedFileName.textContent = file.name;
            uploadVideoBtn.disabled = false;
        }
    });

            // 上传视频
            uploadVideoBtn.addEventListener('click', () => {
                const file = videoUpload.files[0];
                if (!file) return;
                const formData = new FormData();
                formData.append('file', file);
                formData.append('user', user); // 添加用户信息
                // 获取用户输入的宽度
                const width = document.getElementById('videoWidth').value;
                const widthStyle = width ? `style="width: ${width}px;"` : '';

                uploadProgress.style.display = 'block';
                
                fetch('/api/upload_blog_video', {
                    method: 'POST',
                    body: formData
                })
                .then(response => response.json())
                .then(data => {
                    if (data.error) {
                        if (data.error === 'File already exists') {
                            const videoCode = `<div class="video-container" ${widthStyle}><video controls><source src="/api/blog_video/${data.filename}" type="video/mp4"></video></div>`;
                            insertTextAtCursor(videoCode);
                            videoUploadModal.style.display = 'none';
                            alert('视频已存在，但已插入到编辑器中');
                            updateRemainingUploads();
                        } else {
                            throw new Error(data.error);
                        }
                    } else {
                        const videoCode = `<div class="video-container" ${widthStyle}><video controls><source src="/api/blog_video/${data.filename}" type="video/mp4"></video></div>`;
                        insertTextAtCursor(videoCode);
                        videoUploadModal.style.display = 'none';
                        alert('视频上传成功并已插入到编辑器中');
                        updateRemainingUploads();
                    }
                })
                .catch(error => {
                    alert('上传失败: ' + error.message);
                })
                .finally(() => {
                    uploadProgress.style.display = 'none';
                    videoUpload.value = '';
                    selectedFileName.textContent = '';
                    uploadVideoBtn.disabled = true;
                });
            });

            // 插入在线视频
            insertOnlineVideoBtn.addEventListener('click', () => {
                const url = onlineVideoUrl.value.trim();
                // 获取用户输入的宽度
                const width = document.getElementById('videoWidth').value;
                const widthStyle = width ? `style="width: ${width}px;"` : '';

                if (url) {
                    let videoCode = '';
                    if (url.includes('bilibili.com')) {
                        // 提取 Bilibili 视频 ID
                        const bvMatch = url.match(/BV\w+/);
                        if (bvMatch) {
                            const bvid = bvMatch[0];
                            videoCode = `<div class="video-container" ${widthStyle}><iframe src="//player.bilibili.com/player.html?bvid=${bvid}&page=1" scrolling="no" border="0" frameborder="no" framespacing="0" allowfullscreen="true"> </iframe></div>`;
                        } else {
                            alert('无法识别 Bilibili 视频 ID');
                            return;
                        }
                    } else {
                        // 对于其他视频链接，使用普通的 video 标签
                        videoCode = `<div class="video-container" ${widthStyle}><video controls><source src="${url}" type="video/mp4"></video></div>`;
                    }
                    insertTextAtCursor(videoCode);
                    videoUploadModal.style.display = 'none';
                    onlineVideoUrl.value = '';
                } else {
                    alert('请输入有效的视频URL');
                }
            });

    // 在光标位置插入文本
    function insertTextAtCursor(text) {
        const textarea = document.getElementById('content');
        const startPos = textarea.selectionStart;
        const endPos = textarea.selectionEnd;
        const scrollTop = textarea.scrollTop;
        textarea.value = textarea.value.substring(0, startPos) + text + textarea.value.substring(endPos, textarea.value.length);
        textarea.focus();
        textarea.selectionStart = startPos + text.length;
        textarea.selectionEnd = startPos + text.length;
        textarea.scrollTop = scrollTop;
    }

            // 添加一个新函数来更新剩余上传次数
            function updateRemainingUploads() {
                fetch(`/api/get_remaining_uploads/${user}`, {
                    method: 'GET',
                    headers: {
                        'Cache-Control': 'no-cache',
                        'Pragma': 'no-cache'
                    },
                    cache: 'no-store'  // 强制不使用缓存
                })
                .then(response => {
                    if (!response.ok) {
                        throw new Error('Network response was not ok');
                    }
                    return response.json();
                })
                .then(data => {
                    console.log('Remaining uploads updated:', data); // 添加日志
                    document.getElementById('remainingImageUploads').textContent = data.image;
                    document.getElementById('remainingVideoUploads').textContent = data.video;
                    document.getElementById('remainingAttachmentUploads').textContent = data.attachment;
                })
                .catch(error => {
                    console.error('Error fetching remaining uploads:', error);
                });
            }

            // 在页面加载时调用这个函数
            // document.addEventListener('DOMContentLoaded', updateRemainingUploads);

            // 在每次上传后调用更新函数
            function afterUpload() {
                updateRemainingUploads();
            }

            // 定期更新（每分钟）
            setInterval(updateRemainingUploads, 60000);

            // 在页面从后台变为前台时更新
            document.addEventListener('visibilitychange', () => {
                if (document.visibilityState === 'visible') {
                    updateRemainingUploads();
                }
            });


            // 文本框相关代码已移到 initializeTextBoxModal 函数中



</script>
</body>
<!-- 在 body 末尾添加全局颜色下拉菜单容器 -->
<div id="global-color-dropdown" class="color-dropdown-content"></div>
<!-- 在 body 末尾添加全局数学公式下拉菜单容器 -->
<div id="global-math-dropdown" class="dropdown-content"></div>
</html>