<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数学公式渲染测试</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/katex@0.13.11/dist/katex.min.css">
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-title {
            font-weight: bold;
            color: #333;
            margin-bottom: 10px;
        }
    </style>
</head>
<body>
    <h1>数学公式渲染测试</h1>
    
    <div class="test-section">
        <div class="test-title">测试内容：</div>
        <div id="content">
1. **电场强度通量**  
- **高斯定理应用**：  
计算点电荷通过正方形平面的通量 $\Phi_e = \frac{q}{6\epsilon_0}$，需构建高斯面并利用对称性。  
- **数学表达**：$\oint_S \vec{E} \cdot d\vec{S} = \frac{q_{\text{内}}}{\epsilon_0}$

2. **电场分布规律**  
- **球对称电场**：  
均匀带电球体场强分布 
$E = \begin{cases} 
\frac{Qr}{4\pi\epsilon_0 R^3} & (r<R) \\
\frac{Q}{4\pi\epsilon_0 r^2} & (r>R)
\end{cases}$

- **无限大带电平面**：  
多平面叠加场强，区域的场强由电荷面密度代数和决定。

3. **电势与电场力做功**  
- **电势定义** $U = \int \vec{E} \cdot d\vec{l}$：  
计算单位正电荷从O→D的功 $W = q(U_O - U_D)$，需叠加点电荷电势 $U = \frac{1}{4\pi\epsilon_0} \sum \frac{q_i}{r_i}$。  
- **电势-电场关系**：  
由 $U $ 求分量：  
$$
E_x = -\frac{\partial U}{\partial x} , \quad E_y = -\frac{\partial U}{\partial y} 
$$

4. **导体与电介质**  
- **平行板电容器**：  
    - 电位移 $D$ 的连续性：$D = \sigma_{\text{自由}} $  
    - 介质中场强 $E = \frac{D}{\epsilon_0 \epsilon_r}$  
    - 束缚电荷 $\sigma' = P_n = (\epsilon_r - 1)\epsilon_0 E$  
- **电容器储能**：  
    - 串联时 $Q$ 相同：$\frac{W_1}{W_2} = \frac{Q^2/(2C_1)}{Q^2/(2C_2)} = \frac{C_2}{C_1}$  
    - 并联时 $U$ 相同：$\frac{W_1}{W_2} = \frac{\frac{1}{2}C_1U^2}{\frac{1}{2}C_2U^2} $  

---

### （二）恒定磁场考点
1. **安培环路定理**  
- **定理内涵**：  
$\oint_L \vec{H} \cdot d\vec{l} = \sum I_{\text{穿}}$，导线间隔变化时 $\sum I$ 不变，但 $L$ 上 $\vec{B}$ 分布改变。  
- **应用实例**：  
平板电容器充电时，$\oint_{L_1} \vec{H} \cdot d\vec{l} = \frac{d\Phi_D}{dt}$，$\oint_{L_2} \vec{H} \cdot d\vec{l} = 0$（因无位移电流穿过 $L_2$）。
        </div>
    </div>
    
    <div class="test-section">
        <div class="test-title">渲染结果：</div>
        <div id="rendered-content"></div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/marked/marked.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/katex@0.13.11/dist/katex.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/katex@0.13.11/dist/contrib/auto-render.min.js"></script>
    
    <script>
        function testRendering() {
            const content = document.getElementById('content').textContent;
            const renderedDiv = document.getElementById('rendered-content');
            
            // 预处理 LaTeX 公式，参考 view_blog.html 的处理方式
            let processedContent = content;
            
            // 处理 LaTeX 格式的行内公式 \(...\) -> $...$
            processedContent = processedContent.replace(/\\\(([\s\S]*?)\\\)/g, (match, p1) => {
                // 将 * 替换为 \ast，避免被 Markdown 解析为斜体
                const processedFormula = p1.replace(/\*/g, '\\ast');
                return `<span class="math-inline">$${processedFormula}$</span>`;
            });
            
            // 处理 LaTeX 格式的块级公式 \[...\] -> $$...$$
            processedContent = processedContent.replace(/\\\[([\s\S]*?)\\\]/g, (match, p1) => {
                const processedFormula = p1.replace(/\*/g, '\\ast');
                return `<span class="math-display">$$${processedFormula}$$</span>`;
            });

            // 使用marked.js进行Markdown编译
            let html = marked.parse(processedContent);
            
            // 处理引用块样式
            html = html.replace(/<blockquote>/g, '<blockquote style="border-left: 4px solid #667eea !important; margin: 1.5em 0 !important; padding: 1em 1.5em !important; background: #f3f4f6 !important; border-radius: 0 8px 8px 0 !important; font-style: italic !important; color: #4b5563 !important;">');

            renderedDiv.innerHTML = html;

            // 使用 KaTeX 渲染数学公式，参考 view_blog.html 的配置
            if (typeof renderMathInElement !== 'undefined') {
                renderMathInElement(renderedDiv, {
                    delimiters: [
                        {left: "$$", right: "$$", display: true},
                        {left: "$", right: "$", display: false},
                        {left: "\\[", right: "\\]", display: true},
                        {left: "\\(", right: "\\)", display: false}
                    ],
                    throwOnError: false,
                    output: 'htmlAndMathml',
                    trust: true,
                    strict: false,
                    fleqn: true,
                    macros: {
                        "\\eqnarray": "\\begin{aligned}",
                        "\\endeqnarray": "\\end{aligned}"
                    }
                });
            }
        }
        
        // 页面加载完成后执行测试
        window.addEventListener('load', testRendering);
    </script>
</body>
</html>
